[{"id": "1", "project_name": "Velzon - v1.0.0", "tasks_name": "Profile Page Structure", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-1.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-3.jpg"}], "due_date": "25 Jan, 2022", "status": "Inprogress", "priority": "High"}, {"id": "2", "project_name": "Skote - v1.0.0", "tasks_name": "Apologize for shopping Error!", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON><PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-5.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-9.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-10.jpg"}], "due_date": "23 Oct, 2021", "status": "Completed", "priority": "Medium"}, {"id": "3", "project_name": "Minia - v1.0.0", "tasks_name": "Post launch reminder/ Post list", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON><PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-5.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-6.jpg"}, {"assigneeName": "<PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-7.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-8.jpg"}], "due_date": "05 Oct, 2021", "status": "Pending", "priority": "Low"}, {"id": "4", "project_name": "Dason - v1.0.0", "tasks_name": "User research", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-2.jpg"}], "due_date": "11 Dec, 2021", "status": "New", "priority": "Low"}, {"id": "5", "project_name": "Minible - v1.0.0", "tasks_name": "Make a creating an account profile", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-3.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-10.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-9.jpg"}], "due_date": "11 Jan, 2019", "status": "Inprogress", "priority": "Medium"}, {"id": "6", "project_name": "Minimal - v2.1.0", "tasks_name": "Change email option process", "client_name": "<PERSON><PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-6.jpg"}, {"assigneeName": "<PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-7.jpg"}], "due_date": "03 Mar, 2020", "status": "Completed", "priority": "High"}, {"id": "7", "project_name": "Dorsin - <PERSON> Page", "tasks_name": "<PERSON><PERSON> design for FB & Twitter", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-3.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-4.jpg"}, {"assigneeName": "<PERSON><PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-5.jpg"}], "due_date": "26 Feb, 2019", "status": "Pending", "priority": "Medium"}, {"id": "8", "project_name": "Qexal - <PERSON> Page", "tasks_name": "Brand Logo design", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-6.jpg"}, {"assigneeName": "<PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-7.jpg"}, {"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-8.jpg"}], "due_date": "29 Dec, 2021", "status": "Pending", "priority": "High"}, {"id": "9", "project_name": "<PERSON><PERSON> <PERSON> <PERSON><PERSON> Template", "tasks_name": "Additional Calendar", "client_name": "<PERSON>", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-4.jpg"}, {"assigneeName": "<PERSON><PERSON><PERSON>", "assigneeImg": "assets/images/users/avatar-5.jpg"}], "due_date": "13 Jun, 2020", "status": "New", "priority": "Low"}, {"id": "10", "project_name": "Symox v1.0.0", "tasks_name": "Add Dynamic Contact List", "client_name": "RH Nichols", "assignedto": [{"assigneeName": "<PERSON>", "assigneeImg": "assets/images/users/avatar-3.jpg"}], "due_date": "15 Dec, 2020", "status": "Inprogress", "priority": "Medium"}]