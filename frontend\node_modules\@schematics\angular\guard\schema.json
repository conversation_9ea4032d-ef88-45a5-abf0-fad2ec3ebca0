{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularGuard", "title": "Angular Guard Options Schema", "type": "object", "description": "Generates a new, generic route guard definition in the given project.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new route guard.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the guard?"}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new guard.", "default": false}, "flat": {"type": "boolean", "description": "When true (the default), creates the new files at the top level of the current project.", "default": true}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface that defines the guard, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "functional": {"type": "boolean", "description": "Specifies whether to generate a guard as a function.", "default": true}, "implements": {"alias": "guardType", "type": "array", "description": "Specifies which type of guard to create.", "uniqueItems": true, "minItems": 1, "items": {"enum": ["CanActivate", "CanActivateChild", "CanDeactivate", "CanMatch"], "type": "string"}, "default": ["CanActivate"], "x-prompt": "Which type of guard would you like to create?"}}, "required": ["name", "project"]}