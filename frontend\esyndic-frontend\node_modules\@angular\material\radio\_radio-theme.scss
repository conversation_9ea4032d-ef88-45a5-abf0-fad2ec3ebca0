@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use './m2-radio';
@use './m3-radio';
@use 'sass:map';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-radio.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-radio.get-tokens($theme), base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-radio.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-radio.get-tokens($theme, $color-variant), color));
  } @else {
    .mat-mdc-radio-button {
      &.mat-primary {
        @include token-utils.create-token-values-mixed(m2-radio.get-color-tokens($theme, primary));
      }

      &.mat-accent {
        @include token-utils.create-token-values-mixed(m2-radio.get-color-tokens($theme));
      }

      &.mat-warn {
        @include token-utils.create-token-values-mixed(m2-radio.get-color-tokens($theme, warn));
      }
    }
  }
}

/// Outputs typography theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-radio.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-radio.get-typography-tokens($theme));
    }
  }
}

/// Outputs typography theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-radio.get-tokens($theme), density));
  } @else {
    $density-scale: inspection.get-theme-density($theme);

    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-radio.get-density-tokens($theme));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: radio,
      tokens: token-utils.get-overrides(m3-radio.get-tokens(), radio)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-radio') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
