@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/style/elevation';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    menu-container-shape: 4px,
    menu-divider-bottom-spacing: 0,
    menu-divider-top-spacing: 0,
    menu-item-spacing: 16px,
    menu-item-icon-size: 24px,
    menu-item-leading-spacing: 16px,
    menu-item-trailing-spacing: 16px,
    menu-item-with-icon-leading-spacing: 16px,
    menu-item-with-icon-trailing-spacing: 16px,
    menu-container-elevation-shadow: elevation.get-box-shadow(8),

    // Unused
    menu-base-elevation-level: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $active-state-layer-color: inspection.get-theme-color($theme, foreground, base,
    if($is-dark, 0.08, 0.04));
  $text-color: inspection.get-theme-color($theme, foreground, text);

  @return (
    menu-item-label-text-color: $text-color,
    menu-item-icon-color: $text-color,
    menu-item-hover-state-layer-color: $active-state-layer-color,
    menu-item-focus-state-layer-color: $active-state-layer-color,
    menu-container-color: inspection.get-theme-color($theme, background, card),
    menu-divider-color: inspection.get-theme-color($theme, foreground, divider),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    menu-item-label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    menu-item-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    menu-item-label-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    menu-item-label-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    menu-item-label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
