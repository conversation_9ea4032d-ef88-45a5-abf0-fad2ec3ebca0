import { ComponentFixture, TestBed } from '@angular/core/testing';

import { <%= classify(name) %><%= classify(type) %> } from './<%= dasherize(name) %><%= type ? '.' + dasherize(type): '' %>';

describe('<%= classify(name) %><%= classify(type) %>', () => {
  let component: <%= classify(name) %><%= classify(type) %>;
  let fixture: ComponentFixture<<%= classify(name) %><%= classify(type) %>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      <%= standalone ? 'imports' : 'declarations' %>: [<%= classify(name) %><%= classify(type) %>]
    })
    .compileComponents();

    fixture = TestBed.createComponent(<%= classify(name) %><%= classify(type) %>);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
