package com.esyndic.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "apartments", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"building_id", "apartment_number"})
})
public class Apartment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;
    
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "building_id", nullable = false)
    @NotNull(message = "Building is required")
    private Building building;
    
    @Column(name = "apartment_number", nullable = false, length = 10)
    @NotBlank(message = "Apartment number is required")
    private String apartmentNumber;
    
    @Column(name = "floor_number")
    private Integer floorNumber;
    
    @Column(name = "area_sqm", precision = 8, scale = 2)
    @PositiveOrZero(message = "Area must be positive or zero")
    private BigDecimal areaSqm;
    
    @Column
    @PositiveOrZero(message = "Rooms must be positive or zero")
    private Integer rooms;
    
    @Column(name = "monthly_charge", nullable = false, precision = 10, scale = 2)
    @NotNull(message = "Monthly charge is required")
    @PositiveOrZero(message = "Monthly charge must be positive or zero")
    private BigDecimal monthlyCharge = BigDecimal.ZERO;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id")
    private User owner;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resident_id")
    private User resident;
    
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Apartment() {}
    
    public Apartment(Building building, String apartmentNumber, BigDecimal monthlyCharge) {
        this.building = building;
        this.apartmentNumber = apartmentNumber;
        this.monthlyCharge = monthlyCharge;
    }
    
    // Getters and Setters
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public Building getBuilding() {
        return building;
    }
    
    public void setBuilding(Building building) {
        this.building = building;
    }
    
    public String getApartmentNumber() {
        return apartmentNumber;
    }
    
    public void setApartmentNumber(String apartmentNumber) {
        this.apartmentNumber = apartmentNumber;
    }
    
    public Integer getFloorNumber() {
        return floorNumber;
    }
    
    public void setFloorNumber(Integer floorNumber) {
        this.floorNumber = floorNumber;
    }
    
    public BigDecimal getAreaSqm() {
        return areaSqm;
    }
    
    public void setAreaSqm(BigDecimal areaSqm) {
        this.areaSqm = areaSqm;
    }
    
    public Integer getRooms() {
        return rooms;
    }
    
    public void setRooms(Integer rooms) {
        this.rooms = rooms;
    }
    
    public BigDecimal getMonthlyCharge() {
        return monthlyCharge;
    }
    
    public void setMonthlyCharge(BigDecimal monthlyCharge) {
        this.monthlyCharge = monthlyCharge;
    }
    
    public User getOwner() {
        return owner;
    }
    
    public void setOwner(User owner) {
        this.owner = owner;
    }
    
    public User getResident() {
        return resident;
    }
    
    public void setResident(User resident) {
        this.resident = resident;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public String getFullAddress() {
        return building.getName() + " - Apt " + apartmentNumber;
    }
    
    @Override
    public String toString() {
        return "Apartment{" +
                "id=" + id +
                ", apartmentNumber='" + apartmentNumber + '\'' +
                ", floorNumber=" + floorNumber +
                ", monthlyCharge=" + monthlyCharge +
                '}';
    }
}
