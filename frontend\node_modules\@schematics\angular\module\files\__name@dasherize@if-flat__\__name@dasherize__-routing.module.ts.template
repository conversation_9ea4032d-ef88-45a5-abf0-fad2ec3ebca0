import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';<% if (lazyRoute) { %>
import { <%= classify(name) %>Component } from './<%= dasherize(name) %>.component';<% } %>

const routes: Routes = [<% if (lazyRoute) { %>{ path: '', component: <%= classify(name) %>Component }<% } %>];

@NgModule({
  imports: [RouterModule.for<%= routingScope %>(routes)],
  exports: [RouterModule]
})
export class <%= classify(name) %>RoutingModule { }
