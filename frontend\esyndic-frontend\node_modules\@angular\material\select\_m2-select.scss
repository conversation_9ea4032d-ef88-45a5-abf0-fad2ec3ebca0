@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/theming/theming';
@use '../core/style/elevation';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    select-container-elevation-shadow: elevation.get-box-shadow(8),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  $on-surface: if($is-dark, #fff, #000);
  $text-color-base: if(m2-utils.$private-is-internal-build, $on-surface,
    inspection.get-theme-color($theme, foreground, text, 1));
  $disabled-text-color-base: if(m2-utils.$private-is-internal-build, $on-surface,
    inspection.get-theme-color($theme, foreground, disabled-text, 1));
  $icon-color-base: if(m2-utils.$private-is-internal-build, $on-surface,
    inspection.get-theme-color($theme, foreground, divider, 1));

  @return (
    select-panel-background-color: inspection.get-theme-color($theme, background, card),
    select-enabled-trigger-text-color: sass-utils.safe-color-change($text-color-base, $alpha: 0.87),
    select-disabled-trigger-text-color:
      sass-utils.safe-color-change($disabled-text-color-base, $alpha: 0.38),
    select-placeholder-text-color: sass-utils.safe-color-change($text-color-base, $alpha: 0.6),
    select-enabled-arrow-color: sass-utils.safe-color-change($icon-color-base, $alpha: 0.54),
    select-disabled-arrow-color: sass-utils.safe-color-change($text-color-base, $alpha: 0.38),
    select-focused-arrow-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, $palette-name, default), $alpha: 0.87),
    select-invalid-arrow-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, warn, default), $alpha: 0.87),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    select-trigger-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    select-trigger-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    select-trigger-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    select-trigger-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    select-trigger-text-weight: inspection.get-theme-typography($theme, body-1, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -2);

  @return (
    select-arrow-transform: map.get((
      0: translateY(-8px),
      -1: translateY(-8px),
      -2: none,
    ), $density-scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
