<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Progress" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Default Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="default" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="default" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted">Use <code>progress</code> class to show default progress.</p>

              <div class="live-preview">
                  <div class="mb-4">
                      <ngb-progressbar [value]="0"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                    <ngb-progressbar [value]="25"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                    <ngb-progressbar [value]="50"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                    <ngb-progressbar [value]="75"></ngb-progressbar>
                  </div>
                  <div class="">
                    <ngb-progressbar [value]="100"></ngb-progressbar>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup" style="height: 275px;">
<code>&lt;!-- Base Examples --&gt;
&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; aria-valuenow=&quot;0&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 50%&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 75%&quot; aria-valuenow=&quot;75&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;100&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Progress with background color</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="background-show-code" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="background-show-code" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">

              <p class="text-muted">Use <code>bg-</code> class to progress bar class with the below-mentioned color variation to set the background color progress bar.</p>

              <div class="live-preview">
                  <div class="mb-4">
                    <ngb-progressbar [value]="15" type="primary"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                    <ngb-progressbar [value]="25" type="success"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                    <ngb-progressbar [value]="50" type="info"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                    <ngb-progressbar [value]="75" type="warning"></ngb-progressbar>
                  </div>
                  <div class="">
                    <ngb-progressbar [value]="100" type="danger"></ngb-progressbar>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup" style="height: 275px;">
<code>&lt;!-- Backgrounds --&gt;
&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-primary&quot; role=&quot;progressbar&quot; style=&quot;width: 15%&quot; aria-valuenow=&quot;15&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-info&quot; role=&quot;progressbar&quot; style=&quot;width: 50%&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-warning&quot; role=&quot;progressbar&quot; style=&quot;width: 75%&quot; aria-valuenow=&quot;75&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-danger&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;100&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">
  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Progress with Label</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="labelprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="labelprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">

              <p class="text-muted">Add labels to your progress bars by placing text within the progress-bar.</p>

              <div class="live-preview">
                <ngb-progressbar textType="white" [value]="25" [showValue]="true"></ngb-progressbar>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;!-- Labels Example --&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 25%;&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;25%&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Multiple Bars</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="multibarprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="multibarprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted">Use <code>bg-</code> class to progress bar class with the below-mentioned color to change the appearance of individual progress bars.</p>

              <div class="live-preview">
                  <div class="progress">
                      <div class="progress-bar" role="progressbar" style="width: 15%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
                      <div class="progress-bar bg-success" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                      <div class="progress-bar bg-info" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;!-- Multiple Bars --&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 15%&quot; aria-valuenow=&quot;15&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 30%&quot; aria-valuenow=&quot;30&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;div class=&quot;progress-bar bg-info&quot; role=&quot;progressbar&quot; style=&quot;width: 20%&quot; aria-valuenow=&quot;20&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">
  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Height</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="heightprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="heightprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">

              <p class="text-muted">Use <code>progress-sm</code>, <code>progress-lg</code>, or <code>progress-xl</code> class to set the different heights of progress.</p>
              <div class="live-preview">
                  <div class="mb-4">
                      <h5 class="fs-13">Small Progress</h5>
                      <!-- Prgress sm -->
                      <ngb-progressbar [value]="25" class="progress-sm"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                      <h5 class="fs-13">Default Progress </h5>
                      <!-- Prgress md -->
                      <ngb-progressbar [value]="40" type="success" class="progress-md"></ngb-progressbar>
                  </div>
                  <div class="mb-4">
                      <h5 class="fs-13">Large Progress</h5>
                      <!-- Prgress lg -->
                      <div class="">
                        <ngb-progressbar [value]="50" type="warning" class="progress-lg"></ngb-progressbar>
                      </div>
                  </div>
                  <div>
                      <h5 class="fs-13">Extra Large Progress</h5>
                      <!-- Prgress xl -->
                      <div class="">
                        <ngb-progressbar [value]="70" type="danger" class="progress-xl"></ngb-progressbar>
                      </div>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup" style="height: 275px;">
<code>&lt;!-- Prgress sm --&gt;
&lt;div class=&quot;progress progress-sm&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 25%;&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;!-- Prgress Default --&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 40%;&quot; aria-valuenow=&quot;40&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;!-- Prgress lg --&gt;
&lt;div class=&quot;progress progress-lg&quot;&gt;
&lt;div class=&quot;progress-bar bg-warning&quot; role=&quot;progressbar&quot; style=&quot;width: 50%;&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;!-- Prgress xl --&gt;
&lt;div class=&quot;progress progress-xl&quot;&gt;
&lt;div class=&quot;progress-bar bg-danger&quot; role=&quot;progressbar&quot; style=&quot;width: 70%;&quot; aria-valuenow=&quot;70&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Striped Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="stripesprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="stripesprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">

              <p class="text-muted">Use <code>progress-bar-striped</code> class to add strip to the progress.</p>
              <div class="live-preview">
                  <div class="mb-4">
                    <ngb-progressbar [value]="25" [striped]="true"></ngb-progressbar>
                  </div>
                  <div class="">
                    <ngb-progressbar type="success" [value]="40" [striped]="true"></ngb-progressbar>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;!-- Striped Prgress --&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar progress-bar-striped&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar progress-bar-striped bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 40%&quot; aria-valuenow=&quot;40&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->

      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Animated Striped Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="animationstripesprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="animationstripesprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">

              <p class="text-muted">Use <code>progress-bar-striped progress-bar-animated</code> class to add strip with animation to the progress.</p>

              <div class="live-preview">
                <ngb-progressbar [value]="75" [striped] = "true" [animated] = "true" ></ngb-progressbar>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar progress-bar-striped progress-bar-animated&quot; role=&quot;progressbar&quot; aria-valuenow=&quot;75&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot; style=&quot;width: 75%&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">
  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Gradient Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="gradientprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="gradientprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">

              <p class="text-muted">Use <code>bg-gradient</code> class to show gradient progress bar.</p>

              <div class="live-preview">
                  <div class="progress mb-4">
                      <div class="progress-bar bg-primary bg-gradient" role="progressbar" style="width: 15%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress mb-4">
                      <div class="progress-bar bg-success bg-gradient" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress mb-4">
                      <div class="progress-bar bg-info bg-gradient" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress mb-4">
                      <div class="progress-bar bg-warning bg-gradient" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress">
                      <div class="progress-bar bg-danger bg-gradient" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-primary bg-gradient&quot; role=&quot;progressbar&quot; style=&quot;width: 15%&quot; aria-valuenow=&quot;15&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-success bg-gradient&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-info bg-gradient&quot; role=&quot;progressbar&quot; style=&quot;width: 50%&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-warning bg-gradient&quot; role=&quot;progressbar&quot; style=&quot;width: 75%&quot; aria-valuenow=&quot;75&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-danger bg-gradient&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;100&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Animated Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="animatedprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="animatedprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted">Use <code>animated-progess</code> class to show progress with animation.</p>
              <div class="live-preview">
                  <div class="animated-progess mb-4">
                    <ngb-progressbar [value]="15"></ngb-progressbar>
                  </div>
                  <div class="animated-progess mb-4">
                    <ngb-progressbar [value]="25" type="success"></ngb-progressbar>
                  </div>
                  <div class="animated-progess mb-4">
                     <ngb-progressbar [value]="50" type="info"></ngb-progressbar>
                  </div>
                  <div class="animated-progess mb-4">
                    <ngb-progressbar [value]="75" type="warning"></ngb-progressbar>
                  </div>
                  <div class="animated-progess">
                    <ngb-progressbar [value]="100" type="danger"></ngb-progressbar>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;div class=&quot;progress animated-progess&quot;&gt;
&lt;div class=&quot;progress-bar bg-primary&quot; role=&quot;progressbar&quot; style=&quot;width: 15%&quot; aria-valuenow=&quot;15&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess&quot;&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess&quot;&gt;
&lt;div class=&quot;progress-bar bg-info&quot; role=&quot;progressbar&quot; style=&quot;width: 50%&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess&quot;&gt;
&lt;div class=&quot;progress-bar bg-warning&quot; role=&quot;progressbar&quot; style=&quot;width: 75%&quot; aria-valuenow=&quot;75&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess&quot;&gt;
&lt;div class=&quot;progress-bar bg-danger&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;100&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">
  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Custom Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="customprogress-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="customprogress-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted">Use <code>animated-progess custom-progess </code> class to show custom progress with animation.</p>
              <div class="live-preview">
                  <div class="progress animated-progess custom-progress mb-4">
                      <div class="progress-bar bg-primary" role="progressbar" style="width: 15%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress animated-progess custom-progress mb-4">
                      <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress animated-progess custom-progress mb-4">
                      <div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress animated-progess custom-progress mb-4">
                      <div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="progress animated-progess custom-progress">
                      <div class="progress-bar bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;div class=&quot;progress animated-progess custom-progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-primary&quot; role=&quot;progressbar&quot; style=&quot;width: 15%&quot; aria-valuenow=&quot;15&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess custom-progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess custom-progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-info&quot; role=&quot;progressbar&quot; style=&quot;width: 50%&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess custom-progress mb-4&quot;&gt;
&lt;div class=&quot;progress-bar bg-warning&quot; role=&quot;progressbar&quot; style=&quot;width: 75%&quot; aria-valuenow=&quot;75&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress animated-progess custom-progress&quot;&gt;
&lt;div class=&quot;progress-bar bg-danger&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;100&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->

  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Custom Progress with Label</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="customprogresslabel-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="customprogresslabel-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted">Use <code>animated-progess custom-progess progress-label</code> class to show custom progress with animation and label.</p>
              <div class="live-preview">
                  <div class="d-flex align-items-center pb-2 mt-4">
                      <div class="flex-shrink-0 me-3">
                          <div class="avatar-xs">
                              <div class="avatar-title bg-light rounded-circle text-muted fs-16">
                                  <i class="mdi mdi-facebook"></i>
                              </div>
                          </div>
                      </div>
                      <div class="flex-grow-1">
                          <div class="progress animated-progess custom-progress progress-label">
                              <div class="progress-bar bg-primary" role="progressbar" style="width: 15%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"><div class="label">15%</div></div>
                          </div>
                      </div>
                  </div>

                  <div class="d-flex align-items-center py-2">
                      <div class="flex-shrink-0 me-3">
                          <div class="avatar-xs">
                              <div class="avatar-title bg-light rounded-circle text-muted fs-16">
                                  <i class="mdi mdi-twitter"></i>
                              </div>
                          </div>
                      </div>
                      <div class="flex-grow-1">
                          <div class="progress animated-progess custom-progress progress-label">
                              <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"><div class="label">25%</div></div>
                          </div>
                      </div>
                  </div>

                  <div class="d-flex align-items-center py-2">
                      <div class="flex-shrink-0 me-3">
                          <div class="avatar-xs">
                              <div class="avatar-title bg-light rounded-circle text-muted fs-16">
                                  <i class="mdi mdi-github"></i>
                              </div>
                          </div>
                      </div>
                      <div class="flex-grow-1">
                          <div class="progress animated-progess custom-progress progress-label">
                              <div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"><div class="label">30%</div></div>
                          </div>
                      </div>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup" style="height: 275px;">
<code>&lt;div class=&quot;d-flex align-items-center pb-2&quot;&gt;
&lt;div class=&quot;flex-shrink-0 me-3&quot;&gt;
&lt;div class=&quot;avatar-xs&quot;&gt;
&lt;div class=&quot;avatar-title bg-light rounded-circle text-muted fs-16&quot;&gt;
&lt;i class=&quot;mdi mdi-facebook&quot;&gt;&lt;/i&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;flex-grow-1&quot;&gt;
&lt;div class=&quot;progress animated-progess custom-progress progress-label&quot;&gt;
&lt;div class=&quot;progress-bar bg-primary&quot; role=&quot;progressbar&quot; style=&quot;width: 15%&quot; aria-valuenow=&quot;15&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;div class=&quot;label&quot;&gt;15%&lt;/div&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;d-flex align-items-center py-2&quot;&gt;
&lt;div class=&quot;flex-shrink-0 me-3&quot;&gt;
&lt;div class=&quot;avatar-xs&quot;&gt;
&lt;div class=&quot;avatar-title bg-light rounded-circle text-muted fs-16&quot;&gt;
&lt;i class=&quot;mdi mdi-twitter&quot;&gt;&lt;/i&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;flex-grow-1&quot;&gt;
&lt;div class=&quot;progress animated-progess custom-progress progress-label&quot;&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 25%&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;div class=&quot;label&quot;&gt;25%&lt;/div&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;d-flex align-items-center py-2&quot;&gt;
&lt;div class=&quot;flex-shrink-0 me-3&quot;&gt;
&lt;div class=&quot;avatar-xs&quot;&gt;
&lt;div class=&quot;avatar-title bg-light rounded-circle text-muted fs-16&quot;&gt;
&lt;i class=&quot;mdi mdi-github&quot;&gt;&lt;/i&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;flex-grow-1&quot;&gt;
&lt;div class=&quot;progress animated-progess custom-progress progress-label&quot;&gt;
&lt;div class=&quot;progress-bar bg-info&quot; role=&quot;progressbar&quot; style=&quot;width: 50%&quot; aria-valuenow=&quot;50&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;div class=&quot;label&quot;&gt;30%&lt;/div&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div>
  <!-- end col -->
</div>
<!-- end row -->

<div class="row">

  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Content Progress</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="bsprogrescontent-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="bsprogrescontent-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted pb-2">Example of progress with content wrapped in progress.</p>

              <div class="live-preview">

                  <div class="card bg-light overflow-hidden shadow-none">
                      <div class="card-body">
                          <div class="d-flex">
                              <div class="flex-grow-1">
                                  <h6 class="mb-0"><b class="text-secondary">30%</b> Update in progress...</h6>
                              </div>
                              <div class="flex-shrink-0">
                                  <h6 class="mb-0">1 min left</h6>
                              </div>
                          </div>
                      </div>
                      <div class="progress bg-secondary-subtle rounded-0">
                          <div class="progress-bar bg-secondary" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                  </div>

                  <div class="card bg-light overflow-hidden shadow-none">
                      <div class="card-body">
                          <div class="d-flex">
                              <div class="flex-grow-1">
                                  <h6 class="mb-0"><b class="text-success">60%</b> Update in progress...</h6>
                              </div>
                              <div class="flex-shrink-0">
                                  <h6 class="mb-0">45s left</h6>
                              </div>
                          </div>
                      </div>
                      <div class="progress bg-success-subtle rounded-0">
                          <div class="progress-bar bg-success" role="progressbar" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                  </div>

                  <div class="card bg-light overflow-hidden shadow-none">
                      <div class="card-body">
                          <div class="d-flex">
                              <div class="flex-grow-1">
                                  <h6 class="mb-0"><b class="text-danger">82%</b> Update in progress...</h6>
                              </div>
                              <div class="flex-shrink-0">
                                  <h6 class="mb-0">25s left</h6>
                              </div>
                          </div>
                      </div>
                      <div class="progress bg-danger-subtle rounded-0">
                          <div class="progress-bar bg-danger" role="progressbar" style="width: 82%" aria-valuenow="82" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                  </div>

              </div>

              <div class="d-none code-view">
<pre class="language-markup" style="height: 275px;">
<code>&lt;div class=&quot;card bg-light overflow-hidden&quot;&gt;
&lt;div class=&quot;card-body&quot;&gt;
&lt;div class=&quot;d-flex&quot;&gt;
&lt;div class=&quot;flex-grow-1&quot;&gt;
&lt;h6 class=&quot;mb-0&quot;&gt;&lt;b class=&quot;text-secondary&quot;&gt;30%&lt;/b&gt; Update in progress...&lt;/h6&gt;
&lt;/div&gt;
&lt;div class=&quot;flex-shrink-0&quot;&gt;
&lt;h6 class=&quot;mb-0&quot;&gt;1 min left&lt;/h6&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress bg-secondary-subtle rounded-0&quot;&gt;
&lt;div class=&quot;progress-bar bg-secondary&quot; role=&quot;progressbar&quot; style=&quot;width: 30%&quot; aria-valuenow=&quot;30&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;card bg-light overflow-hidden&quot;&gt;
&lt;div class=&quot;card-body&quot;&gt;
&lt;div class=&quot;d-flex&quot;&gt;
&lt;div class=&quot;flex-grow-1&quot;&gt;
&lt;h6 class=&quot;mb-0&quot;&gt;&lt;b class=&quot;text-success&quot;&gt;60%&lt;/b&gt; Update in progress...&lt;/h6&gt;
&lt;/div&gt;
&lt;div class=&quot;flex-shrink-0&quot;&gt;
&lt;h6 class=&quot;mb-0&quot;&gt;45s left&lt;/h6&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress bg-success-subtle rounded-0&quot;&gt;
&lt;div class=&quot;progress-bar bg-success&quot; role=&quot;progressbar&quot; style=&quot;width: 60%&quot; aria-valuenow=&quot;60&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;</code>

<code>&lt;div class=&quot;card bg-light overflow-hidden&quot;&gt;
&lt;div class=&quot;card-body&quot;&gt;
&lt;div class=&quot;d-flex&quot;&gt;
&lt;div class=&quot;flex-grow-1&quot;&gt;
&lt;h6 class=&quot;mb-0&quot;&gt;&lt;b class=&quot;text-danger&quot;&gt;82%&lt;/b&gt; Update in progress...&lt;/h6&gt;
&lt;/div&gt;
&lt;div class=&quot;flex-shrink-0&quot;&gt;
&lt;h6 class=&quot;mb-0&quot;&gt;25s left&lt;/h6&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;div class=&quot;progress bg-danger-subtle rounded-0&quot;&gt;
&lt;div class=&quot;progress-bar bg-danger&quot; role=&quot;progressbar&quot; style=&quot;width: 82%&quot; aria-valuenow=&quot;82&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->
  </div><!-- end col -->

  <div class="col-xxl-6">
      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Progress with Steps</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="bsprogressexample-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="bsprogressexample-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted pb-2">Here is the example of progress which is represented by steps completion.</p>

              <div class="live-preview">
                  <div class="position-relative m-4">
                      <div class="progress" style="height: 1px;">
                          <div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                      <button type="button" class="position-absolute top-0 start-0 translate-middle btn btn-sm btn-primary rounded-pill" style="width: 2rem; height:2rem;">1</button>
                      <button type="button" class="position-absolute top-0 start-50 translate-middle btn btn-sm btn-primary rounded-pill" style="width: 2rem; height:2rem;">2</button>
                      <button type="button" class="position-absolute top-0 start-100 translate-middle btn btn-sm btn-light rounded-pill" style="width: 2rem; height:2rem;">3</button>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;div class=&quot;position-relative m-4&quot;&gt;
&lt;div class=&quot;progress&quot; style=&quot;height: 1px;&quot;&gt;
&lt;div class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 50%;&quot; aria-valuenow=&quot;25&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;button type=&quot;button&quot; class=&quot;position-absolute top-0 start-0 translate-middle btn btn-sm btn-primary rounded-pill&quot; style=&quot;width: 2rem; height:2rem;&quot;&gt;1&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;position-absolute top-0 start-50 translate-middle btn btn-sm btn-primary rounded-pill&quot; style=&quot;width: 2rem; height:2rem;&quot;&gt;2&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;position-absolute top-0 start-100 translate-middle btn btn-sm btn-light rounded-pill&quot; style=&quot;width: 2rem; height:2rem;&quot;&gt;3&lt;/button&gt;
&lt;/div&gt;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->

      <div class="card">
          <div class="card-header align-items-center d-flex">
              <h4 class="card-title mb-0 flex-grow-1">Step Progress with Arrow</h4>
              <div class="flex-shrink-0">
                  <div class="form-check form-switch form-switch-right form-switch-md">
                      <label for="stepprogressexample-showcode" class="form-label text-muted">Show Code</label>
                      <input class="form-check-input code-switcher" type="checkbox" id="stepprogressexample-showcode" (click)="ShowCode($event)">
                  </div>
              </div>
          </div><!-- end card header -->

          <div class="card-body">
              <p class="text-muted pb-2">Use <code>progress-step-arrow </code>class to create step progress with an arrow.</p>

              <div class="live-preview">
                  <div class="progress progress-step-arrow progress-info">
                      <div class="progress-bar" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">Step 1</div>
                      <div class="progress-bar" role="progressbar" style="width: 100%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"> Step 2</div>
                      <div class="progress-bar bg-light text-body" role="progressbar" style="width: 100%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"> Step 3</div>
                  </div>
              </div>

              <div class="d-none code-view">
<pre class="language-markup">
<code>&lt;div class=&quot;progress progress-step-arrow progress-info&quot;&gt;
&lt;a href=&quot;javascript:void(0);&quot; class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;100&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt;Step 1&lt;/a&gt;
&lt;a href=&quot;javascript:void(0);&quot; class=&quot;progress-bar&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;30&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt; Step 2&lt;/a&gt;
&lt;a href=&quot;javascript:void(0);&quot; class=&quot;progress-bar bg-light text-body&quot; role=&quot;progressbar&quot; style=&quot;width: 100%&quot; aria-valuenow=&quot;20&quot; aria-valuemin=&quot;0&quot; aria-valuemax=&quot;100&quot;&gt; Step 3&lt;/a&gt;
&lt;/div&gt;;</code></pre>
              </div>
          </div><!-- end card-body -->
      </div><!-- end card -->

  </div>
  <!-- end col -->

</div>
<!-- end row -->
