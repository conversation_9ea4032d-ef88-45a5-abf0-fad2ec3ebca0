{"description": "Package versions used by schematics in @schematics/angular.", "comment": "This file is needed so that dependencies are synced by Renovate.", "private": true, "dependencies": {"@angular/core": "^18.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "browser-sync": "^3.0.0", "express": "^4.18.2", "jasmine-core": "~5.2.0", "jasmine-spec-reporter": "~7.0.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine-html-reporter": "~2.1.0", "karma-jasmine": "~5.1.0", "karma": "~6.4.0", "less": "^4.2.0", "ng-packagr": "^18.2.0", "postcss": "^8.4.38", "protractor": "~7.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "ts-node": "~10.9.0", "typescript": "~5.5.2", "zone.js": "~0.14.10"}}