import { TestBed } from '@angular/core/testing';
import { <%= guardType %> } from '@angular/router';

import { <%= camelize(name) %>Guard } from './<%= dasherize(name) %>.guard';

describe('<%= camelize(name) %>Guard', () => {
  const executeGuard: <%= guardType %> = (...guardParameters) => 
      TestBed.runInInjectionContext(() => <%= camelize(name) %>Guard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
