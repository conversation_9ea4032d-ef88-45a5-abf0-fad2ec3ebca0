server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: esyndic-backend
  
  datasource:
    url: ****************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  security:
    user:
      name: admin
      password: admin

# JWT Configuration
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000 # 24 hours in milliseconds

# File Upload Configuration
file:
  upload:
    dir: ./uploads
    max-size: 10MB

# Paymee Configuration
paymee:
  api:
    base-url: https://api.paymee.tn
    version: v1
    timeout: 30000 # 30 seconds
  credentials:
    # These should be configured via environment variables in production
    api-key: ${PAYMEE_API_KEY:your-paymee-api-key}
    secret-key: ${PAYMEE_SECRET_KEY:your-paymee-secret-key}

# Logging Configuration
logging:
  level:
    com.esyndic: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/esyndic-backend.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# CORS Configuration
cors:
  allowed-origins: 
    - http://localhost:4200
    - http://localhost:3000
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true
