@use 'sass:map';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use './m2-grid-list';
@use './m3-grid-list';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-grid-list.get-tokens($theme), base));
  } @else {
  }
}

// Include this empty mixin for consistency with the other components.
@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-grid-list.get-tokens($theme), color));
  } @else {
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-grid-list.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-grid-list.get-typography-tokens($theme));
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-grid-list.get-tokens($theme), density));
  } @else {
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: grid-list,
      tokens: token-utils.get-overrides(m3-grid-list.get-tokens(), grid-list)
    ),
  );
}

@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-grid-list') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
