@use '../tokens/m2-utils';
@use '../theming/inspection';
@use '../style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $active-state-layer-color: inspection.get-theme-color($theme, foreground, base,
    if($is-dark, 0.08, 0.04));

  @return (
    option-selected-state-label-text-color: inspection.get-theme-color($theme, $palette-name),
    option-label-text-color: inspection.get-theme-color($theme, foreground, text),
    option-hover-state-layer-color: $active-state-layer-color,
    option-focus-state-layer-color: $active-state-layer-color,
    option-selected-state-layer-color: $active-state-layer-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    option-label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    option-label-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    option-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    option-label-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    option-label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
