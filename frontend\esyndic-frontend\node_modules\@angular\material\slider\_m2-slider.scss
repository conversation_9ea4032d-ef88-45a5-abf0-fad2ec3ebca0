@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use '../core/style/elevation';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    slider-active-track-height: 6px,
    slider-active-track-shape: 9999px,
    slider-handle-elevation: elevation.get-box-shadow(1),
    slider-handle-height: 20px,
    slider-handle-shape: 50%,
    slider-handle-width: 20px,
    slider-inactive-track-height: 4px,
    slider-inactive-track-shape: 9999px,
    slider-value-indicator-border-radius: 4px,
    slider-value-indicator-caret-display: block,
    slider-value-indicator-container-transform: translateX(-50%),
    slider-value-indicator-height: 32px,
    slider-value-indicator-padding: 0 12px,
    slider-value-indicator-text-transform: none,
    slider-value-indicator-width: auto,
    slider-with-overlap-handle-outline-width: 1px,
    slider-with-tick-marks-active-container-opacity: 0.6,
    slider-with-tick-marks-container-shape: 50%,
    slider-with-tick-marks-container-size: 2px,
    slider-with-tick-marks-inactive-container-opacity: 0.6,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $theme-color-tokens: private-get-color-palette-color-tokens($theme, primary);
  $is-dark: inspection.get-theme-type($theme) == dark;
  $elevation: inspection.get-theme-color($theme, foreground, elevation);
  $on-surface: if($is-dark, #fff, #000);

  @return map.merge(
    $theme-color-tokens,
    (
      slider-disabled-active-track-color: $on-surface,
      slider-disabled-handle-color: $on-surface,
      slider-disabled-inactive-track-color: $on-surface,
      slider-label-container-color: $on-surface,
      slider-label-label-text-color: if($is-dark, #000, #fff),
      slider-value-indicator-opacity: if($is-dark, 0.9, 0.6),
      slider-with-overlap-handle-outline-color: #fff,
      slider-with-tick-marks-disabled-container-color: $on-surface,
    ),
  );
}

// Generates tokens for the slider properties that change based on the theme.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $color: inspection.get-theme-color($theme, $palette-name);
  $hover-ripple-color: if($color == null, null, #{rgba($color, 0.05)});
  $focus-ripple-color: if($color == null, null, #{rgba($color, 0.2)});
  $on-color: inspection.get-theme-color($theme, $palette-name, default-contrast);

  @return (
    slider-active-track-color: $color,
    slider-focus-handle-color: $color,
    slider-focus-state-layer-color: $focus-ripple-color,
    slider-handle-color: $color,
    slider-hover-handle-color: $color,
    slider-hover-state-layer-color: $hover-ripple-color,
    slider-inactive-track-color: $color,
    slider-ripple-color: $color,
    slider-with-tick-marks-active-container-color: $on-color,
    slider-with-tick-marks-inactive-container-color: $color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    slider-label-label-text-font: inspection.get-theme-typography($theme, subtitle-2, font-family),
    slider-label-label-text-size: inspection.get-theme-typography($theme, subtitle-2, font-size),
    slider-label-label-text-line-height:
        inspection.get-theme-typography($theme, subtitle-2, line-height),
    slider-label-label-text-tracking:
        inspection.get-theme-typography($theme, subtitle-2, letter-spacing),
    slider-label-label-text-weight:
        inspection.get-theme-typography($theme, subtitle-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
    get-unthemable-tokens(),
    get-color-tokens(m2-utils.$placeholder-color-config),
    get-typography-tokens(m2-utils.$placeholder-typography-config),
    get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
