@use 'sass:map';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/m2-utils';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    table-row-item-outline-width: 1px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    table-background-color: inspection.get-theme-color($theme, background, 'card'),

    table-header-headline-color: inspection.get-theme-color($theme, foreground, text),
    table-row-item-label-text-color: inspection.get-theme-color($theme, foreground, text),
    table-row-item-outline-color: inspection.get-theme-color($theme, foreground, divider),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  $cell-font-family: inspection.get-theme-typography($theme, body-2, font-family);
  $cell-line-height: inspection.get-theme-typography($theme, body-2, line-height);
  $cell-font-size: inspection.get-theme-typography($theme, body-2, font-size);
  $cell-font-weight: inspection.get-theme-typography($theme, body-2, font-weight);
  $cell-letter-spacing: inspection.get-theme-typography($theme, body-2, letter-spacing);

  @return (
    table-header-headline-font: inspection.get-theme-typography($theme, subtitle-2, font-family),
    table-header-headline-line-height:
        inspection.get-theme-typography($theme, subtitle-2, line-height),
    table-header-headline-size: inspection.get-theme-typography($theme, subtitle-2, font-size),
    table-header-headline-weight: inspection.get-theme-typography($theme, subtitle-2, font-weight),
    table-header-headline-tracking:
        inspection.get-theme-typography($theme, subtitle-2, letter-spacing),

    // Plain cells and footer cells have the same typography.
    table-row-item-label-text-font: $cell-font-family,
    table-row-item-label-text-line-height: $cell-line-height,
    table-row-item-label-text-size: $cell-font-size,
    table-row-item-label-text-weight: $cell-font-weight,
    table-row-item-label-text-tracking: $cell-letter-spacing,

    table-footer-supporting-text-font: $cell-font-family,
    table-footer-supporting-text-line-height: $cell-line-height,
    table-footer-supporting-text-size: $cell-font-size,
    table-footer-supporting-text-weight: $cell-font-weight,
    table-footer-supporting-text-tracking: $cell-letter-spacing,
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $header-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 44px,
    -4: 40px
  );
  $cell-scale: (
    0: 52px,
    -1: 48px,
    -2: 44px,
    -3: 40px,
    -4: 36px
  );

  @return (
    table-header-container-height: map.get($header-scale, $scale),
    table-footer-container-height: map.get($cell-scale, $scale),
    table-row-item-container-height: map.get($cell-scale, $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
          get-unthemable-tokens(),
          get-color-tokens(m2-utils.$placeholder-color-config),
          get-typography-tokens(m2-utils.$placeholder-typography-config),
          get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
