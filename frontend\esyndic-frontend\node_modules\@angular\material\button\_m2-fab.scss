@use 'sass:meta';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/style/elevation';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    fab-container-elevation-shadow: elevation.get-box-shadow(6),
    fab-container-shape: 50%,
    fab-extended-container-elevation-shadow: elevation.get-box-shadow(6),
    fab-extended-container-height: 48px,
    fab-extended-container-shape: 24px,
    fab-extended-focus-container-elevation-shadow: elevation.get-box-shadow(8),
    fab-extended-hover-container-elevation-shadow: elevation.get-box-shadow(8),
    fab-extended-pressed-container-elevation-shadow: elevation.get-box-shadow(12),
    fab-focus-container-elevation-shadow: elevation.get-box-shadow(8),
    fab-hover-container-elevation-shadow: elevation.get-box-shadow(8),
    fab-pressed-container-elevation-shadow: elevation.get-box-shadow(12),
    fab-small-container-elevation-shadow: elevation.get-box-shadow(6),
    fab-small-container-shape: 50%,
    fab-small-focus-container-elevation-shadow: elevation.get-box-shadow(8),
    fab-small-hover-container-elevation-shadow: elevation.get-box-shadow(8),
    fab-small-pressed-container-elevation-shadow: elevation.get-box-shadow(12),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  @return (
    fab-container-color: inspection.get-theme-color($theme, background, card),
    fab-disabled-state-container-color:
        inspection.get-theme-color($theme, background, disabled-button, 0.12),
    fab-disabled-state-foreground-color:
        inspection.get-theme-color($theme, foreground, disabled-button, if($is-dark, 0.5, 0.38)),
    fab-disabled-state-layer-color: inspection.get-theme-color($theme, foreground, base),
    fab-focus-state-layer-opacity: if($is-dark, 0.24, 0.12),
    fab-foreground-color: inspection.get-theme-color($theme, foreground, base),
    fab-hover-state-layer-opacity: if($is-dark, 0.08, 0.04),
    fab-pressed-state-layer-opacity: if($is-dark, 0.24, 0.12),
    fab-ripple-color: inspection.get-theme-color($theme, foreground, base, 0.1),
    fab-small-container-color: inspection.get-theme-color($theme, background, card),
    fab-small-disabled-state-container-color:
        inspection.get-theme-color($theme, background, disabled-button, 0.12),
    fab-small-disabled-state-foreground-color:
        inspection.get-theme-color($theme, foreground, disabled-button, if($is-dark, 0.5, 0.38)),
    fab-small-disabled-state-layer-color: inspection.get-theme-color($theme, foreground, base),
    fab-small-focus-state-layer-opacity: if($is-dark, 0.24, 0.12),
    fab-small-foreground-color: inspection.get-theme-color($theme, foreground, base),
    fab-small-hover-state-layer-opacity: if($is-dark, 0.08, 0.04),
    fab-small-pressed-state-layer-opacity: if($is-dark, 0.24, 0.12),
    fab-small-ripple-color: inspection.get-theme-color($theme, foreground, base, 0.1),
    fab-small-state-layer-color: inspection.get-theme-color($theme, foreground, base),
    fab-state-layer-color: inspection.get-theme-color($theme, foreground, base),
  );
}

// Generates the mapping for the properties that change based on the FAB palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  $foreground-color: null;
  $state-layer-color: null;
  $ripple-color: null;
  $contrast-color: inspection.get-theme-color($theme, $palette-name, default-contrast);

  @if (m2-utils.$private-is-internal-build or
      meta.type-of($contrast-color) != 'color') {
    $is-dark: inspection.get-theme-type($theme) == dark;
    $container-color: inspection.get-theme-color($theme, $palette-name);
    $contrast-tone: m2-utils.contrast-tone($container-color, $is-dark);
    $color: if($contrast-tone == 'dark', #000, #fff);
    $foreground-color: $color;
    $state-layer-color: $color;
    $ripple-color: rgba($color, 0.1);
  }
  @else {
    $foreground-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
    $state-layer-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
    $ripple-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 0.1);
  }

  @return (
    fab-container-color: inspection.get-theme-color($theme, $palette-name, default),
    fab-foreground-color: $foreground-color,
    fab-ripple-color: $ripple-color,
    fab-small-container-color: inspection.get-theme-color($theme, $palette-name, default),
    fab-small-foreground-color: $foreground-color,
    fab-small-ripple-color: $ripple-color,
    fab-small-state-layer-color: $state-layer-color,
    fab-state-layer-color: $state-layer-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    fab-extended-label-text-font: inspection.get-theme-typography($theme, button, font-family),
    fab-extended-label-text-size: inspection.get-theme-typography($theme, button, font-size),
    fab-extended-label-text-tracking:
        inspection.get-theme-typography($theme, button, letter-spacing),
    fab-extended-label-text-weight: inspection.get-theme-typography($theme, button, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    fab-small-touch-target-display: if($density-scale < -1, none, block),
    fab-touch-target-display: if($density-scale < -1, none, block),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
