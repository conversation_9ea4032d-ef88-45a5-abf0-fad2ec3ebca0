import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject } from 'rxjs';
import Keycloak from 'keycloak-js';

export interface UserProfile {
  id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  realmRoles: string[];
  resourceRoles: { [key: string]: string[] };
}

export interface AuthState {
  isAuthenticated: boolean;
  user: UserProfile | null;
  token: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private keycloak = inject(Keycloak);
  private router = inject(Router);
  
  private authStateSubject = new BehaviorSubject<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null
  });

  public authState$ = this.authStateSubject.asObservable();

  constructor() {
    this.initializeAuthState();
  }

  private async initializeAuthState(): Promise<void> {
    if (this.keycloak.authenticated) {
      const userProfile = await this.loadUserProfile();
      this.authStateSubject.next({
        isAuthenticated: true,
        user: userProfile,
        token: this.keycloak.token || null
      });
    }
  }

  private async loadUserProfile(): Promise<UserProfile> {
    try {
      const profile = await this.keycloak.loadUserProfile();
      const realmRoles = this.keycloak.realmAccess?.roles || [];
      const keycloakResourceAccess = this.keycloak.resourceAccess || {};

      // Convert Keycloak resource access to our format
      const resourceRoles: { [key: string]: string[] } = {};
      Object.keys(keycloakResourceAccess).forEach(key => {
        resourceRoles[key] = keycloakResourceAccess[key]?.roles || [];
      });

      // Extract all roles
      const allRoles = [
        ...realmRoles,
        ...Object.values(resourceRoles).flat()
      ];

      return {
        id: profile.id,
        username: profile.username,
        email: profile.email,
        firstName: profile.firstName,
        lastName: profile.lastName,
        roles: allRoles,
        realmRoles,
        resourceRoles
      };
    } catch (error) {
      console.error('Error loading user profile:', error);
      return {
        roles: [],
        realmRoles: [],
        resourceRoles: {}
      };
    }
  }

  // Authentication methods
  async login(): Promise<void> {
    try {
      await this.keycloak.login({
        redirectUri: window.location.origin
      });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.keycloak.logout({
        redirectUri: window.location.origin
      });
      this.authStateSubject.next({
        isAuthenticated: false,
        user: null,
        token: null
      });
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Token management
  getToken(): string | null {
    return this.keycloak.token || null;
  }

  async refreshToken(): Promise<boolean> {
    try {
      const refreshed = await this.keycloak.updateToken(30);
      if (refreshed) {
        const currentState = this.authStateSubject.value;
        this.authStateSubject.next({
          ...currentState,
          token: this.keycloak.token || null
        });
      }
      return refreshed;
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }

  // Role-based access control
  hasRole(role: string): boolean {
    const currentState = this.authStateSubject.value;
    return currentState.user?.roles.includes(role) || false;
  }

  hasAnyRole(roles: string[]): boolean {
    return roles.some(role => this.hasRole(role));
  }

  hasRealmRole(role: string): boolean {
    const currentState = this.authStateSubject.value;
    return currentState.user?.realmRoles.includes(role) || false;
  }

  hasResourceRole(resource: string, role: string): boolean {
    const currentState = this.authStateSubject.value;
    const resourceRoles = currentState.user?.resourceRoles[resource] || [];
    return resourceRoles.includes(role);
  }

  // User information
  getCurrentUser(): UserProfile | null {
    return this.authStateSubject.value.user;
  }

  isAuthenticated(): boolean {
    return this.authStateSubject.value.isAuthenticated;
  }

  // Navigation helpers
  navigateToLogin(): void {
    this.login();
  }

  navigateAfterLogin(): void {
    const user = this.getCurrentUser();
    if (!user) return;

    // Route based on user roles
    if (this.hasRealmRole('SUPERADMIN')) {
      this.router.navigate(['/admin/dashboard']);
    } else if (this.hasRealmRole('ADMIN')) {
      this.router.navigate(['/admin/buildings']);
    } else if (this.hasRealmRole('PRESIDENT')) {
      this.router.navigate(['/president/dashboard']);
    } else if (this.hasRealmRole('OWNER') || this.hasRealmRole('RESIDENT')) {
      this.router.navigate(['/resident/dashboard']);
    } else {
      this.router.navigate(['/dashboard']);
    }
  }
}
