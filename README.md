# e-Syndic - Building Management System

A comprehensive full-stack web application for managing co-owned buildings (copropriétés) with digital syndic management capabilities.

## 🏗️ Architecture

- **Frontend**: Angular 17+ with Angular Material
- **Backend**: Spring Boot 3.x (Java)
- **Database**: PostgreSQL
- **Authentication**: JWT + Role-based access control
- **Payment Integration**: Paymee (Tunisia) API

## 🎯 Features

### User Management
- Multi-role system (SUPERADMIN, ADMIN, PRESIDENT, OWNER, RESIDENT)
- JWT-based authentication
- Role-based access control

### Building Management
- Building and apartment tracking
- User assignment to apartments
- Excel import for bulk account creation

### General Assemblies & Voting
- Assembly creation and management
- Digital voting system (YES/NO/ABSTAIN)
- Quorum tracking and presence management
- Automated report generation

### Payment System
- Manual payment entry
- Online payments via Paymee integration
- FIFO logic for charge assignment
- Payment history tracking

### Expense Management
- Expense tracking with categorization
- Invoice upload support (PDF/images)
- Supplier management

### Claims Management
- Resident claim submission with photo upload
- Admin claim management
- Status tracking (Pending, In Progress, Resolved)

### Role-based Dashboards
- Customized dashboards per user role
- Real-time statistics and notifications

## 📁 Project Structure

```
e-syndic/
├── backend/                 # Spring Boot application
│   ├── src/main/java/
│   │   └── com/esyndic/
│   │       ├── entity/      # JPA entities
│   │       ├── repository/  # Data repositories
│   │       ├── service/     # Business logic
│   │       ├── controller/  # REST controllers
│   │       └── config/      # Configuration classes
│   └── src/main/resources/
├── frontend/                # Angular application
│   ├── src/app/
│   │   ├── auth/           # Authentication module
│   │   ├── dashboard/      # Dashboard components
│   │   ├── buildings/      # Building management
│   │   ├── assemblies/     # General assemblies
│   │   ├── payments/       # Payment management
│   │   ├── expenses/       # Expense tracking
│   │   └── claims/         # Claims management
│   └── src/assets/
└── database/               # Database scripts
    └── schema.sql
```

## 🚀 Getting Started

### Prerequisites
- Java 17+
- Node.js 18+
- PostgreSQL 13+
- Angular CLI 17+

### Backend Setup
```bash
cd backend
./mvnw spring-boot:run
```

### Frontend Setup
```bash
cd frontend
npm install
ng serve
```

### Database Setup
```bash
psql -U postgres -d esyndic -f database/schema.sql
```

## 🔐 User Roles

- **SUPERADMIN**: Full system control, building creation, user management
- **ADMIN**: Building management, user management, assemblies, payments
- **PRESIDENT**: Vote approval and decision making
- **OWNER**: View charges, payment history, participate in assemblies
- **RESIDENT**: Access housing info, submit claims

## 📊 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh

### User Management
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `PUT /api/users/{id}` - Update user

### Building Management
- `GET /api/buildings` - List buildings
- `POST /api/buildings` - Create building
- `GET /api/apartments` - List apartments

### Assemblies
- `POST /api/assemblies` - Create assembly
- `POST /api/assemblies/vote` - Submit vote

### Payments
- `POST /api/payments/manual` - Manual payment entry
- `POST /api/payments/paymee` - Paymee payment initiation

### Expenses
- `GET /api/expenses` - List expenses
- `POST /api/expenses` - Create expense

### Claims
- `GET /api/claims` - List claims
- `POST /api/claims` - Submit claim
- `PUT /api/claims/status` - Update claim status

## 💳 Paymee Integration

The application integrates with Paymee payment gateway for online payments:
- API Documentation: https://www.paymee.tn/swagger.html
- Endpoint: `POST /api/v1/payments/create`
- Features: QR code payments, redirect URLs, webhook confirmations

## 🧪 Testing

### Backend Tests
```bash
cd backend
./mvnw test
```

### Frontend Tests
```bash
cd frontend
ng test
```

## 📝 License

This project is licensed under the MIT License.
