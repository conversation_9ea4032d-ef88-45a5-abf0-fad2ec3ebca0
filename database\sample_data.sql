-- Sample Data for e-Syndic Application
-- Run this after schema.sql to populate the database with test data

-- Insert sample users
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, role, is_active) VALUES
-- SUPERADMIN
('550e8400-e29b-41d4-a716-446655440001', 'superadmin', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Super', 'Admin', '+216 20 123 456', 'SUPERADMIN', true),

-- ADMIN users
('550e8400-e29b-41d4-a716-446655440002', 'admin1', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', '<PERSON>', '<PERSON>', '+216 20 234 567', 'ADMIN', true),
('550e8400-e29b-41d4-a716-446655440003', 'admin2', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Fatma', 'Trabelsi', '+216 20 345 678', 'ADMIN', true),

-- PRESIDENT
('550e8400-e29b-41d4-a716-446655440004', 'president1', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Mohamed', 'Gharbi', '+216 20 456 789', 'PRESIDENT', true),

-- OWNER users
('550e8400-e29b-41d4-a716-446655440005', 'owner1', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Salma', 'Bouaziz', '+216 20 567 890', 'OWNER', true),
('550e8400-e29b-41d4-a716-446655440006', 'owner2', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Karim', 'Mansouri', '+216 20 678 901', 'OWNER', true),
('550e8400-e29b-41d4-a716-446655440007', 'owner3', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Leila', 'Hamdi', '+216 20 789 012', 'OWNER', true),

-- RESIDENT users
('550e8400-e29b-41d4-a716-446655440008', 'resident1', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Youssef', 'Khelifi', '+216 20 890 123', 'RESIDENT', true),
('550e8400-e29b-41d4-a716-446655440009', 'resident2', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Amina', 'Sassi', '+216 20 901 234', 'RESIDENT', true),
('550e8400-e29b-41d4-a716-446655440010', 'resident3', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.Uo0.', 'Sami', 'Jebali', '+216 20 012 345', 'RESIDENT', true);

-- Insert sample buildings
INSERT INTO buildings (id, name, address, city, postal_code, total_apartments, construction_year, description, created_by) VALUES
('660e8400-e29b-41d4-a716-446655440001', 'Résidence Les Jasmins', '15 Avenue Habib Bourguiba', 'Tunis', '1000', 24, 2018, 'Modern residential building with elevator and parking', '550e8400-e29b-41d4-a716-446655440001'),
('660e8400-e29b-41d4-a716-446655440002', 'Immeuble El Manar', '42 Rue de la République', 'Ariana', '2080', 18, 2015, 'Family-friendly building in quiet neighborhood', '550e8400-e29b-41d4-a716-446655440001'),
('660e8400-e29b-41d4-a716-446655440003', 'Tour Carthage', '88 Boulevard 14 Janvier', 'Tunis', '1001', 36, 2020, 'High-rise building with sea view', '550e8400-e29b-41d4-a716-446655440001');

-- Assign admins to buildings
INSERT INTO building_admins (building_id, admin_id) VALUES
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002'),
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002'),
('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003');

-- Insert sample apartments
INSERT INTO apartments (id, building_id, apartment_number, floor_number, area_sqm, rooms, monthly_charge, owner_id, resident_id) VALUES
-- Résidence Les Jasmins apartments
('770e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'A101', 1, 85.5, 3, 250.00, '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440005'),
('770e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', 'A102', 1, 92.0, 3, 280.00, '550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440008'),
('770e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440001', 'A201', 2, 85.5, 3, 250.00, '550e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440009'),
('770e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440001', 'A202', 2, 92.0, 3, 280.00, '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004'),

-- Immeuble El Manar apartments
('770e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440002', 'B101', 1, 75.0, 2, 200.00, '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440010'),
('770e8400-e29b-41d4-a716-446655440006', '660e8400-e29b-41d4-a716-446655440002', 'B102', 1, 80.0, 3, 220.00, '550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440006');

-- Insert sample charges (last 6 months)
INSERT INTO charges (apartment_id, amount, charge_month, due_date, is_paid) VALUES
-- Apartment A101 charges
('770e8400-e29b-41d4-a716-446655440001', 250.00, '2024-01-01', '2024-01-31', true),
('770e8400-e29b-41d4-a716-446655440001', 250.00, '2024-02-01', '2024-02-29', true),
('770e8400-e29b-41d4-a716-446655440001', 250.00, '2024-03-01', '2024-03-31', false),
('770e8400-e29b-41d4-a716-446655440001', 250.00, '2024-04-01', '2024-04-30', false),
('770e8400-e29b-41d4-a716-446655440001', 250.00, '2024-05-01', '2024-05-31', false),
('770e8400-e29b-41d4-a716-446655440001', 250.00, '2024-06-01', '2024-06-30', false),

-- Apartment A102 charges
('770e8400-e29b-41d4-a716-446655440002', 280.00, '2024-01-01', '2024-01-31', true),
('770e8400-e29b-41d4-a716-446655440002', 280.00, '2024-02-01', '2024-02-29', true),
('770e8400-e29b-41d4-a716-446655440002', 280.00, '2024-03-01', '2024-03-31', true),
('770e8400-e29b-41d4-a716-446655440002', 280.00, '2024-04-01', '2024-04-30', false),
('770e8400-e29b-41d4-a716-446655440002', 280.00, '2024-05-01', '2024-05-31', false),
('770e8400-e29b-41d4-a716-446655440002', 280.00, '2024-06-01', '2024-06-30', false);

-- Insert sample payments
INSERT INTO payments (id, apartment_id, amount, payment_method, payment_status, payment_date, reference_number, description, created_by) VALUES
('880e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', 500.00, 'MANUAL', 'COMPLETED', '2024-02-15 10:30:00', 'PAY-2024-001', 'Payment for January and February charges', '550e8400-e29b-41d4-a716-446655440002'),
('880e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440002', 840.00, 'PAYMEE', 'COMPLETED', '2024-03-20 14:45:00', 'PAY-2024-002', 'Payment for January, February, and March charges', '550e8400-e29b-41d4-a716-446655440002');

-- Insert sample expenses
INSERT INTO expenses (building_id, amount, category, supplier_name, supplier_contact, description, expense_date, created_by) VALUES
('660e8400-e29b-41d4-a716-446655440001', 1200.00, 'MAINTENANCE', 'Société de Maintenance SARL', '+216 71 123 456', 'Elevator maintenance and repair', '2024-06-15', '550e8400-e29b-41d4-a716-446655440002'),
('660e8400-e29b-41d4-a716-446655440001', 350.00, 'CLEANING', 'Clean Pro Services', '+216 71 234 567', 'Monthly cleaning supplies', '2024-06-10', '550e8400-e29b-41d4-a716-446655440002'),
('660e8400-e29b-41d4-a716-446655440001', 800.00, 'UTILITIES', 'STEG', '+216 71 345 678', 'Electricity bill for common areas', '2024-06-05', '550e8400-e29b-41d4-a716-446655440002');

-- Insert sample assembly
INSERT INTO assemblies (id, building_id, title, description, scheduled_date, location, moderator_id, status, quorum_required, total_eligible_voters, created_by) VALUES
('990e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'Assemblée Générale Ordinaire 2024', 'Assemblée générale annuelle pour approuver les comptes et élire le nouveau conseil syndical', '2024-07-15 18:00:00', 'Salle de réunion - Rez-de-chaussée', '550e8400-e29b-41d4-a716-446655440004', 'SCHEDULED', 50, 24, '550e8400-e29b-41d4-a716-446655440002');

-- Insert agenda items for the assembly
INSERT INTO agenda_items (assembly_id, title, description, order_index, requires_vote) VALUES
('990e8400-e29b-41d4-a716-446655440001', 'Approbation des comptes 2023', 'Présentation et approbation des comptes de l''exercice 2023', 1, true),
('990e8400-e29b-41d4-a716-446655440001', 'Budget prévisionnel 2024', 'Présentation et vote du budget prévisionnel pour 2024', 2, true),
('990e8400-e29b-41d4-a716-446655440001', 'Travaux de rénovation', 'Discussion sur les travaux de rénovation de la façade', 3, true),
('990e8400-e29b-41d4-a716-446655440001', 'Élection du conseil syndical', 'Élection des membres du nouveau conseil syndical', 4, true);

-- Insert sample claims
INSERT INTO claims (building_id, apartment_id, submitted_by, title, description, status, priority) VALUES
('660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440005', 'Fuite d''eau dans la salle de bain', 'Il y a une fuite d''eau importante dans la salle de bain qui cause des dégâts au plafond', 'PENDING', 3),
('660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440008', 'Problème d''éclairage dans le couloir', 'L''éclairage du couloir du 1er étage ne fonctionne plus depuis une semaine', 'IN_PROGRESS', 2),
('660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440009', 'Bruit excessif des voisins', 'Les voisins du dessus font beaucoup de bruit tard le soir', 'PENDING', 1);

-- Update some statistics
UPDATE assemblies SET 
    total_eligible_voters = (SELECT COUNT(*) FROM apartments WHERE building_id = '660e8400-e29b-41d4-a716-446655440001')
WHERE id = '990e8400-e29b-41d4-a716-446655440001';
