import { ComponentHarness, HarnessPredicate } from '@angular/cdk/testing';
import { coerceBooleanProperty } from '@angular/cdk/coercion';

/** Harness for interacting with a mat-checkbox in tests. */
class MatCheckboxHarness extends ComponentHarness {
    static hostSelector = '.mat-mdc-checkbox';
    _input = this.locatorFor('input');
    _label = this.locatorFor('label');
    _inputContainer = this.locatorFor('.mdc-checkbox');
    /**
     * Gets a `HarnessPredicate` that can be used to search for a checkbox with specific attributes.
     * @param options Options for narrowing the search:
     *   - `selector` finds a checkbox whose host element matches the given selector.
     *   - `label` finds a checkbox with specific label text.
     *   - `name` finds a checkbox with specific name.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return (new HarnessPredicate(this, options)
            .addOption('label', options.label, (harness, label) => HarnessPredicate.stringMatches(harness.getLabelText(), label))
            // We want to provide a filter option for "name" because the name of the checkbox is
            // only set on the underlying input. This means that it's not possible for developers
            // to retrieve the harness of a specific checkbox with name through a CSS selector.
            .addOption('name', options.name, async (harness, name) => (await harness.getName()) === name)
            .addOption('checked', options.checked, async (harness, checked) => (await harness.isChecked()) == checked)
            .addOption('disabled', options.disabled, async (harness, disabled) => {
            return (await harness.isDisabled()) === disabled;
        }));
    }
    /** Whether the checkbox is checked. */
    async isChecked() {
        const checked = (await this._input()).getProperty('checked');
        return coerceBooleanProperty(await checked);
    }
    /** Whether the checkbox is in an indeterminate state. */
    async isIndeterminate() {
        const indeterminate = (await this._input()).getProperty('indeterminate');
        return coerceBooleanProperty(await indeterminate);
    }
    /** Whether the checkbox is disabled. */
    async isDisabled() {
        const input = await this._input();
        const disabled = await input.getAttribute('disabled');
        if (disabled !== null) {
            return coerceBooleanProperty(disabled);
        }
        return (await input.getAttribute('aria-disabled')) === 'true';
    }
    /** Whether the checkbox is required. */
    async isRequired() {
        const required = (await this._input()).getProperty('required');
        return coerceBooleanProperty(await required);
    }
    /** Whether the checkbox is valid. */
    async isValid() {
        const invalid = (await this.host()).hasClass('ng-invalid');
        return !(await invalid);
    }
    /** Gets the checkbox's name. */
    async getName() {
        return (await this._input()).getAttribute('name');
    }
    /** Gets the checkbox's value. */
    async getValue() {
        return (await this._input()).getProperty('value');
    }
    /** Gets the checkbox's aria-label. */
    async getAriaLabel() {
        return (await this._input()).getAttribute('aria-label');
    }
    /** Gets the checkbox's aria-labelledby. */
    async getAriaLabelledby() {
        return (await this._input()).getAttribute('aria-labelledby');
    }
    /** Gets the checkbox's label text. */
    async getLabelText() {
        return (await this._label()).text();
    }
    /** Focuses the checkbox. */
    async focus() {
        return (await this._input()).focus();
    }
    /** Blurs the checkbox. */
    async blur() {
        return (await this._input()).blur();
    }
    /** Whether the checkbox is focused. */
    async isFocused() {
        return (await this._input()).isFocused();
    }
    /**
     * Toggles the checked state of the checkbox.
     *
     * Note: This attempts to toggle the checkbox as a user would, by clicking it. Therefore if you
     * are using `MAT_CHECKBOX_DEFAULT_OPTIONS` to change the behavior on click, calling this method
     * might not have the expected result.
     */
    async toggle() {
        const elToClick = await ((await this.isDisabled()) ? this._inputContainer() : this._input());
        return elToClick.click();
    }
    /**
     * Puts the checkbox in a checked state by toggling it if it is currently unchecked, or doing
     * nothing if it is already checked.
     *
     * Note: This attempts to check the checkbox as a user would, by clicking it. Therefore if you
     * are using `MAT_CHECKBOX_DEFAULT_OPTIONS` to change the behavior on click, calling this method
     * might not have the expected result.
     */
    async check() {
        if (!(await this.isChecked())) {
            await this.toggle();
        }
    }
    /**
     * Puts the checkbox in an unchecked state by toggling it if it is currently checked, or doing
     * nothing if it is already unchecked.
     *
     * Note: This attempts to uncheck the checkbox as a user would, by clicking it. Therefore if you
     * are using `MAT_CHECKBOX_DEFAULT_OPTIONS` to change the behavior on click, calling this method
     * might not have the expected result.
     */
    async uncheck() {
        if (await this.isChecked()) {
            await this.toggle();
        }
    }
}

export { MatCheckboxHarness };
//# sourceMappingURL=testing.mjs.map
