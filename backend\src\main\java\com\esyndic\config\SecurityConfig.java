package com.esyndic.config;

import com.esyndic.security.CustomUserDetailsService;
import com.esyndic.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers("/auth/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                
                // SUPERADMIN only endpoints
                .requestMatchers("/users/create-admin", "/buildings/create").hasRole("SUPERADMIN")
                
                // ADMIN and SUPERADMIN endpoints
                .requestMatchers("/users/**", "/buildings/**", "/apartments/**").hasAnyRole("ADMIN", "SUPERADMIN")
                .requestMatchers("/assemblies/**", "/expenses/**").hasAnyRole("ADMIN", "SUPERADMIN")
                .requestMatchers("/payments/manual").hasAnyRole("ADMIN", "SUPERADMIN")
                .requestMatchers("/claims/manage/**").hasAnyRole("ADMIN", "SUPERADMIN")
                
                // PRESIDENT endpoints
                .requestMatchers("/assemblies/approve/**", "/assemblies/vote/**").hasAnyRole("PRESIDENT", "ADMIN", "SUPERADMIN")
                
                // OWNER endpoints
                .requestMatchers("/payments/my-payments", "/charges/my-charges").hasAnyRole("OWNER", "ADMIN", "SUPERADMIN")
                .requestMatchers("/assemblies/participate/**").hasAnyRole("OWNER", "PRESIDENT", "ADMIN", "SUPERADMIN")
                
                // RESIDENT endpoints
                .requestMatchers("/claims/submit", "/claims/my-claims").hasAnyRole("RESIDENT", "OWNER", "ADMIN", "SUPERADMIN")
                
                // All authenticated users
                .requestMatchers("/dashboard/**", "/profile/**").authenticated()
                .requestMatchers("/payments/paymee/**").authenticated()
                
                .anyRequest().authenticated()
            )
            .authenticationProvider(authenticationProvider())
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("http://localhost:4200", "http://localhost:3000"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
