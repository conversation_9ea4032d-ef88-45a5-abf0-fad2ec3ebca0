{"$schema": "http://json-schema.org/draft-07/schema", "$id": "BuilderInputSchema", "title": "Input schema for builders.", "type": "object", "properties": {"workspaceRoot": {"type": "string"}, "currentDirectory": {"type": "string"}, "id": {"type": "number"}, "target": {"type": "object", "properties": {"project": {"type": "string"}, "target": {"type": "string"}, "configuration": {"type": "string"}}, "required": ["project", "target"]}, "info": {"type": "object"}, "options": {"type": "object"}}, "required": ["currentDirectory", "id", "info", "workspaceRoot"]}