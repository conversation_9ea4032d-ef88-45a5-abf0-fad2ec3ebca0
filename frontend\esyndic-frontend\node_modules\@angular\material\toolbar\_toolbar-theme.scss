@use 'sass:map';
@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use '../core/theming/theming';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use './m2-toolbar';
@use './m3-toolbar';

@mixin _palette-styles($theme, $palette-name) {
  @include token-utils.create-token-values-mixed(
    m2-toolbar.private-get-color-palette-color-tokens(
      $background-color: inspection.get-theme-color($theme, $palette-name),
      $text-color: inspection.get-theme-color($theme, $palette-name, default-contrast)
    )
  );
}

@mixin base($theme) {
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-toolbar.get-tokens($theme), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-toolbar.get-color-tokens($theme));
    }

    .mat-toolbar {
      &.mat-primary {
        @include _palette-styles($theme, primary);
      }

      &.mat-accent {
        @include _palette-styles($theme, accent);
      }

      &.mat-warn {
        @include _palette-styles($theme, warn);
      }
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-toolbar.get-tokens($theme), typography));
  } @else {
    // TODO(mmalerba): Stop calling this and resolve resulting screen diffs.
    $theme: inspection.private-get-typography-back-compat-theme($theme);

    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-toolbar.get-typography-tokens($theme));
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-toolbar.get-tokens($theme), density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-toolbar.get-density-tokens($theme));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: toolbar,
      tokens: token-utils.get-overrides(m3-toolbar.get-tokens(), toolbar)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-toolbar') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
