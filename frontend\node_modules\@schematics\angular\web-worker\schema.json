{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularWebWorker", "title": "Angular Web Worker Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic web worker definition in the given project.", "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the worker file, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name of the worker.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the worker?"}, "snippet": {"type": "boolean", "default": true, "description": "Add a worker creation snippet in a sibling file of the same name."}}, "required": ["name", "project"]}