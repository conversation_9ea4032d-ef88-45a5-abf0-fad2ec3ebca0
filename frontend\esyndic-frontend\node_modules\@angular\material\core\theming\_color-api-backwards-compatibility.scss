@use '../../badge/badge-theme';
@use '../../button/button-theme';
@use '../../button/fab-theme';
@use '../../button/icon-button-theme';
@use '../../checkbox/checkbox-theme';
@use '../../chips/chips-theme';
@use '../../datepicker/datepicker-theme';
@use '../../icon/icon-theme';
@use '../../progress-bar/progress-bar-theme';
@use '../../progress-spinner/progress-spinner-theme';
@use '../../radio/radio-theme';
@use '../../select/select-theme';
@use '../../slide-toggle/slide-toggle-theme';
@use '../../slider/slider-theme';
@use '../../stepper/stepper-theme';
@use '../../tabs/tabs-theme';
@use '../../form-field/form-field-theme';
@use '../option/option-theme';
@use '../selection/pseudo-checkbox/pseudo-checkbox-theme';

// We want to emit only the overrides, because the backwards compatibility styles are usually
// emitted after all the tokens have been included once already. This allows us to save ~50kb
// from the bundle.
$_overrides-only: true;

@mixin _color-variant-styles($theme, $color-variant) {
  $secondary-when-primary: if($color-variant == primary, secondary, $color-variant);

  & {
    @if ($color-variant != primary) {
      @include option-theme.color($theme, $secondary-when-primary);
    }
  }

  & {
    @if ($color-variant != primary) {
      @include progress-spinner-theme.color($theme, $color-variant);
    }
  }

  & {
    @if ($color-variant != primary) {
      @include pseudo-checkbox-theme.color($theme, $color-variant);
    }
  }

  & {
    @include stepper-theme.color($theme, $color-variant);
  }

  &.mat-icon {
    @include icon-theme.color($theme, $color-variant);
  }

  &.mat-mdc-checkbox {
    @if ($color-variant != primary) {
      @include checkbox-theme.color($theme, $color-variant);
    }
  }

  &.mat-mdc-slider {
    @if ($color-variant != primary) {
      @include slider-theme.color($theme, $color-variant);
    }
  }

  &.mat-mdc-tab-group,
  &.mat-mdc-tab-nav-bar {
      @include tabs-theme.color($theme, $color-variant);
  }

  &.mat-mdc-slide-toggle {
    @include slide-toggle-theme.color($theme, $color-variant);
  }

  &.mat-mdc-form-field {
    @if ($color-variant != primary) {
      @include select-theme.color($theme, $color-variant);
    }
  }

  &.mat-mdc-radio-button {
    @if ($color-variant != primary) {
      @include radio-theme.color($theme, $color-variant);
    }
  }

  &.mat-mdc-progress-bar {
    @if ($color-variant != primary) {
      @include progress-bar-theme.color($theme, $color-variant);
    }
  }

  &.mat-mdc-form-field {
    @if ($color-variant != primary) {
      @include form-field-theme.color($theme, $color-variant);
    }
  }

  &.mat-datepicker-content {
    @include datepicker-theme.color($theme, $color-variant);
  }

  &.mat-mdc-button-base {
    @if ($color-variant != primary) {
      @include button-theme.color($theme, $color-variant);
    }
    @include icon-button-theme.color($theme, $color-variant);
  }

  &.mat-mdc-standard-chip {
    @if ($color-variant != primary) {
      @include chips-theme.color($theme, $secondary-when-primary);
    }
  }

  .mdc-list-item__start,
  .mdc-list-item__end {
    @if ($color-variant != primary) {
      @include checkbox-theme.color($theme, $color-variant);
      @include radio-theme.color($theme, $color-variant);
    }
  }

  // M3 dropped support for warn/error color FABs.
  @if $color-variant != error and $color-variant != primary {
    &.mat-mdc-fab,
    &.mat-mdc-mini-fab {
      @include fab-theme.color($theme, $color-variant);
    }
  }
}

@mixin color-variants-backwards-compatibility($theme) {
  .mat-primary {
    @include _color-variant-styles($theme, primary);
  }
  .mat-badge {
    @include badge-theme.color($theme, $color-variant: primary);
  }

  .mat-accent {
    @include _color-variant-styles($theme, tertiary);
  }
  .mat-badge-accent {
    @include badge-theme.color($theme, $color-variant: tertiary);
  }

  .mat-warn {
    @include _color-variant-styles($theme, error);
  }
  .mat-badge-warn {
    @include badge-theme.color($theme, $color-variant: error);
  }
}
