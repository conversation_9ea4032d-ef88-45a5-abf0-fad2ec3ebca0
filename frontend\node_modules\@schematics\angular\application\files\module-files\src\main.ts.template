<% if(!!viewEncapsulation) { %>import { ViewEncapsulation } from '@angular/core';
<% }%>import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';

platformBrowserDynamic().bootstrapModule(AppModule, {
  ngZoneEventCoalescing: true<% if(!!viewEncapsulation) { %>,
  defaultEncapsulation: ViewEncapsulation.<%= viewEncapsulation %><% } %>
})
  .catch(err => console.error(err));
