{"schematics": {"use-application-builder": {"version": "18.0.0", "factory": "./use-application-builder/migration", "description": "Migrate application projects to the new build system. Application projects that are using the '@angular-devkit/build-angular' package's 'browser' and/or 'browser-esbuild' builders will be migrated to use the new 'application' builder. You can read more about this, including known issues and limitations, here: https://angular.dev/tools/cli/build-system-migration", "optional": true, "documentation": "tools/cli/build-system-migration"}}}