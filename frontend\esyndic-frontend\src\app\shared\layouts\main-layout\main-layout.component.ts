import { Component, OnInit, OnD<PERSON>roy, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatListModule } from '@angular/material/list';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Subject, takeUntil } from 'rxjs';

import { AuthService, UserProfile } from '../../../core/services/auth.service';


interface MenuItem {
  label: string;
  icon: string;
  route?: string;
  children?: MenuItem[];
  roles?: string[];
  realmRoles?: string[];
  badge?: number;
  divider?: boolean;
}

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatListModule,
    MatBadgeModule,
    MatTooltipModule
  ],
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss']
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  private authService = inject(AuthService);
  private breakpointObserver = inject(BreakpointObserver);
  private destroy$ = new Subject<void>();

  // Signals for reactive state management
  isHandset = signal(false);
  sidenavOpened = signal(true);
  currentUser = signal<UserProfile | null>(null);
  
  // Computed properties
  userDisplayName = computed(() => {
    const user = this.currentUser();
    if (!user) return 'Guest';
    return user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`
      : user.username || user.email || 'User';
  });

  userInitials = computed(() => {
    const user = this.currentUser();
    if (!user) return 'G';
    if (user.firstName && user.lastName) {
      return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
    }
    return (user.username || user.email || 'U').charAt(0).toUpperCase();
  });

  // Menu configuration based on Velzon template structure
  menuItems: MenuItem[] = [
    {
      label: 'Dashboard',
      icon: 'dashboard',
      route: '/dashboard'
    },
    {
      label: 'Buildings Management',
      icon: 'apartment',
      realmRoles: ['ADMIN', 'SUPERADMIN'],
      children: [
        { label: 'All Buildings', icon: 'list', route: '/admin/buildings' },
        { label: 'Add Building', icon: 'add', route: '/admin/buildings/add' },
        { label: 'Building Reports', icon: 'assessment', route: '/admin/buildings/reports' }
      ]
    },
    {
      label: 'User Management',
      icon: 'people',
      realmRoles: ['ADMIN', 'SUPERADMIN'],
      children: [
        { label: 'All Users', icon: 'list', route: '/admin/users' },
        { label: 'Add User', icon: 'person_add', route: '/admin/users/add' },
        { label: 'User Roles', icon: 'admin_panel_settings', route: '/admin/users/roles' }
      ]
    },
    {
      label: 'Financial Management',
      icon: 'account_balance',
      realmRoles: ['ADMIN', 'PRESIDENT', 'SUPERADMIN'],
      children: [
        { label: 'Charges', icon: 'receipt', route: '/finance/charges' },
        { label: 'Payments', icon: 'payment', route: '/finance/payments' },
        { label: 'Expenses', icon: 'money_off', route: '/finance/expenses' },
        { label: 'Financial Reports', icon: 'trending_up', route: '/finance/reports' }
      ]
    },
    {
      label: 'Assemblies',
      icon: 'groups',
      realmRoles: ['ADMIN', 'PRESIDENT', 'OWNER', 'SUPERADMIN'],
      children: [
        { label: 'All Assemblies', icon: 'event', route: '/assemblies' },
        { label: 'Create Assembly', icon: 'add_circle', route: '/assemblies/create', realmRoles: ['ADMIN', 'PRESIDENT', 'SUPERADMIN'] },
        { label: 'Voting History', icon: 'how_to_vote', route: '/assemblies/votes' }
      ]
    },
    {
      label: 'Claims & Maintenance',
      icon: 'build',
      children: [
        { label: 'My Claims', icon: 'report_problem', route: '/claims/my' },
        { label: 'Submit Claim', icon: 'add_box', route: '/claims/submit' },
        { label: 'All Claims', icon: 'list_alt', route: '/claims/all', realmRoles: ['ADMIN', 'PRESIDENT', 'SUPERADMIN'] }
      ]
    },
    { divider: true, label: '', icon: '' },
    {
      label: 'My Profile',
      icon: 'account_circle',
      route: '/profile'
    },
    {
      label: 'Settings',
      icon: 'settings',
      route: '/settings'
    }
  ];

  ngOnInit(): void {
    // Monitor screen size
    this.breakpointObserver.observe([Breakpoints.Handset])
      .pipe(takeUntil(this.destroy$))
      .subscribe(result => {
        this.isHandset.set(result.matches);
        if (result.matches) {
          this.sidenavOpened.set(false);
        } else {
          this.sidenavOpened.set(true);
        }
      });

    // Monitor auth state
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        this.currentUser.set(authState.user);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidenav(): void {
    this.sidenavOpened.update(opened => !opened);
  }

  async logout(): Promise<void> {
    try {
      await this.authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  hasMenuAccess(item: MenuItem): boolean {
    if (!item.realmRoles && !item.roles) return true;
    
    if (item.realmRoles) {
      return this.authService.hasAnyRole(item.realmRoles);
    }
    
    if (item.roles) {
      return this.authService.hasAnyRole(item.roles);
    }
    
    return false;
  }
}
