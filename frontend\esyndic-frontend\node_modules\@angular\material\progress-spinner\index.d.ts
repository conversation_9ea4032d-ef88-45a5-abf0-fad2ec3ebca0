import { M as MatProgressSpinner } from '../progress-spinner.d-Lfz4Wh5x.js';
export { b as MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, c as MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, a as MatProgressSpinnerDefaultOptions, d as <PERSON><PERSON>pinner, P as ProgressSpinnerMode } from '../progress-spinner.d-Lfz4Wh5x.js';
import * as i0 from '@angular/core';
import { M as MatCommonModule } from '../common-module.d-C8xzHJDr.js';
import '../palette.d-BSSFKjO6.js';
import '@angular/cdk/bidi';

declare class MatProgressSpinnerModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatProgressSpinnerModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatProgressSpinnerModule, never, [typeof MatProgressSpinner, typeof MatProgressSpinner], [typeof MatProgressSpinner, typeof MatProgressSpinner, typeof MatCommonModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatProgressSpinnerModule>;
}

export { MatProgressSpinner, MatProgressSpinnerModule };
