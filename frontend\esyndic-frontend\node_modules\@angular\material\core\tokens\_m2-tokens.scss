@use '../../autocomplete/m2-autocomplete';
@use '../../badge/m2-badge';
@use '../../bottom-sheet/m2-bottom-sheet';
@use '../../button-toggle/m2-button-toggle';
@use '../../button/m2-button';
@use '../../button/m2-fab';
@use '../../button/m2-icon-button';
@use '../../card/m2-card';
@use '../../checkbox/m2-checkbox';
@use '../../chips/m2-chip';
@use '../../datepicker/m2-datepicker';
@use '../../dialog/m2-dialog';
@use '../../divider/m2-divider';
@use '../../expansion/m2-expansion';
@use '../../form-field/m2-form-field';
@use '../../grid-list/m2-grid-list';
@use '../../icon/m2-icon';
@use '../../list/m2-list';
@use '../../menu/m2-menu';
@use '../../paginator/m2-paginator';
@use '../../progress-bar/m2-progress-bar';
@use '../../progress-spinner/m2-progress-spinner';
@use '../../radio/m2-radio';
@use '../../select/m2-select';
@use '../../sidenav/m2-sidenav';
@use '../../slide-toggle/m2-slide-toggle';
@use '../../slider/m2-slider';
@use '../../snack-bar/m2-snack-bar';
@use '../../sort/m2-sort';
@use '../../stepper/m2-stepper';
@use '../../table/m2-table';
@use '../../tabs/m2-tabs';
@use '../../timepicker/m2-timepicker';
@use '../../toolbar/m2-toolbar';
@use '../../tooltip/m2-tooltip';
@use '../../tree/m2-tree';
@use '../m2-app';
@use '../option/m2-optgroup';
@use '../option/m2-option';
@use '../ripple/m2-ripple';
@use '../selection/pseudo-checkbox/m2-pseudo-checkbox';
@use '../style/sass-utils';
@use '../theming/inspection';
@use 'sass:map';
@use 'sass:meta';

/// Gets the tokens for the given theme, m2 tokens module, and theming system.
/// @param {Map} $theme The Angular Material theme object to generate token values from.
/// @param {String} $module The Sass module containing the token getter functions.
/// @param {String} $system The theming system to get tokens for. Valid values are: unthemable,
///     color, typography, density.
/// @return {Map} The token map by calling the token getter for the given system in the given module
///    with the given Angular Material theme. Token names are not fully-qualified.
@function _get-tokens-for-module-and-system($theme, $module, $system) {
  @if $system == unthemable {
    @return meta.call(meta.get-function(get-#{$system}-tokens, $module: $module));
  }
  @if not inspection.theme-has($theme, $system) {
    @return ();
  }
  @return meta.call(meta.get-function(get-#{$system}-tokens, $module: $module), $theme);
}

/// Gets the fully qualified tokens map for the given theme and m2 tokens module.
/// @param {Map} $theme The Angular Material theme object to generate token values from.
/// @param {String} $module The Sass module containing the token getter functions.
/// @return {Map} The token map by calling the token getters in the given module with the given
///     Angular Material theme. Token names are fully-qualified.
@function _get-tokens-for-module($theme, $module) {
  $tokens: sass-utils.deep-merge-all(
    _get-tokens-for-module-and-system($theme, $module, unthemable),
    _get-tokens-for-module-and-system($theme, $module, color),
    _get-tokens-for-module-and-system($theme, $module, typography),
    _get-tokens-for-module-and-system($theme, $module, density)
  );
  @return map.set((), map.get(meta.module-variables($module), prefix), $tokens);
}

/// Gets the full set of M2 tokens for the given theme object.
/// @param {Map} $theme The Angular Material theme object to generate token values from.
/// @return {Map} The token map for the given Angular Material theme. Returned format:
///     (
///       (fully, qualified, namespace): (
///         token: value
///       )
///     )
@function m2-tokens-from-theme($theme) {
  @return sass-utils.deep-merge-all(
    _get-tokens-for-module($theme, m2-app),
    _get-tokens-for-module($theme, m2-autocomplete),
    _get-tokens-for-module($theme, m2-badge),
    _get-tokens-for-module($theme, m2-bottom-sheet),
    _get-tokens-for-module($theme, m2-button),
    _get-tokens-for-module($theme, m2-button-toggle),
    _get-tokens-for-module($theme, m2-card),
    _get-tokens-for-module($theme, m2-checkbox),
    _get-tokens-for-module($theme, m2-chip),
    _get-tokens-for-module($theme, m2-datepicker),
    _get-tokens-for-module($theme, m2-dialog),
    _get-tokens-for-module($theme, m2-divider),
    _get-tokens-for-module($theme, m2-expansion),
    _get-tokens-for-module($theme, m2-fab),
    _get-tokens-for-module($theme, m2-form-field),
    _get-tokens-for-module($theme, m2-grid-list),
    _get-tokens-for-module($theme, m2-icon),
    _get-tokens-for-module($theme, m2-icon-button),
    _get-tokens-for-module($theme, m2-list),
    _get-tokens-for-module($theme, m2-menu),
    _get-tokens-for-module($theme, m2-optgroup),
    _get-tokens-for-module($theme, m2-option),
    _get-tokens-for-module($theme, m2-paginator),
    _get-tokens-for-module($theme, m2-progress-bar),
    _get-tokens-for-module($theme, m2-progress-spinner),
    _get-tokens-for-module($theme, m2-pseudo-checkbox),
    _get-tokens-for-module($theme, m2-radio),
    _get-tokens-for-module($theme, m2-ripple),
    _get-tokens-for-module($theme, m2-select),
    _get-tokens-for-module($theme, m2-sidenav),
    _get-tokens-for-module($theme, m2-slide-toggle),
    _get-tokens-for-module($theme, m2-slider),
    _get-tokens-for-module($theme, m2-snack-bar),
    _get-tokens-for-module($theme, m2-sort),
    _get-tokens-for-module($theme, m2-stepper),
    _get-tokens-for-module($theme, m2-tabs),
    _get-tokens-for-module($theme, m2-table),
    _get-tokens-for-module($theme, m2-timepicker),
    _get-tokens-for-module($theme, m2-toolbar),
    _get-tokens-for-module($theme, m2-tooltip),
    _get-tokens-for-module($theme, m2-tree),
  );
}
