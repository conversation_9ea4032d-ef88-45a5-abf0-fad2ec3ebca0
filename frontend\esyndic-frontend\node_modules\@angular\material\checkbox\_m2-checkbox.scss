@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/m2/theming' as m2-theming;
@use '../core/m2/palette' as m2-palette;

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    checkbox-disabled-selected-checkmark-color: #fff,
    checkbox-selected-focus-state-layer-opacity: 0.16,
    checkbox-selected-hover-state-layer-opacity: 0.04,
    checkbox-selected-pressed-state-layer-opacity: 0.16,
    checkbox-unselected-focus-state-layer-opacity: 0.16,
    checkbox-unselected-hover-state-layer-opacity: 0.04,
    checkbox-unselected-pressed-state-layer-opacity: 0.16,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: accent, $exclude: ()) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $foreground-base: inspection.get-theme-color($theme, foreground, base);
  $palette-default: inspection.get-theme-color($theme, $palette-name, default);
  $disabled-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, foreground, base), $alpha: 0.38);
  $palette-selected: inspection.get-theme-color($theme, $palette-name);
  $border-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, foreground, base), $alpha: 0.54);
  $active-border-color:
    m2-theming.get-color-from-palette(m2-palette.$gray-palette, if($is-dark, 200, 900));
  $selected-checkmark-color: null;

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  @if (m2-utils.$private-is-internal-build) {
    $contrast-tone: m2-utils.contrast-tone($palette-selected, $is-dark);
    $selected-checkmark-color: if($contrast-tone == 'dark', #000, #fff);
  }
  @else {
    $selected-checkmark-color:
      inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
  }

  $tokens: (
    checkbox-disabled-label-color: inspection.get-theme-color($theme, foreground, disabled-text),
    checkbox-label-text-color: inspection.get-theme-color($theme, foreground, text),
    checkbox-disabled-selected-icon-color: $disabled-color,
    checkbox-disabled-unselected-icon-color: $disabled-color,
    checkbox-selected-checkmark-color: $selected-checkmark-color,
    checkbox-selected-focus-icon-color: $palette-selected,
    checkbox-selected-hover-icon-color: $palette-selected,
    checkbox-selected-icon-color: $palette-selected,
    checkbox-selected-pressed-icon-color: $palette-selected,
    checkbox-unselected-focus-icon-color: $active-border-color,
    checkbox-unselected-hover-icon-color: $active-border-color,
    checkbox-unselected-icon-color: $border-color,
    checkbox-selected-focus-state-layer-color: $palette-default,
    checkbox-selected-hover-state-layer-color: $palette-default,
    checkbox-selected-pressed-state-layer-color: $palette-default,
    checkbox-unselected-focus-state-layer-color: $foreground-base,
    checkbox-unselected-hover-state-layer-color: $foreground-base,
    checkbox-unselected-pressed-state-layer-color: $foreground-base,
  );

  @each $token in $exclude {
    $tokens: map.remove($tokens, $token);
  }

  @return $tokens;
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    checkbox-label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    checkbox-label-text-line-height: inspection.get-theme-typography($theme, body-2, line-height),
    checkbox-label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    checkbox-label-text-tracking: inspection.get-theme-typography($theme, body-2, letter-spacing),
    checkbox-label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    checkbox-touch-target-display: if($scale < -1, none, block),
    checkbox-state-layer-size: map.get((
      0: 40px,
      -1: 36px,
      -2: 32px,
      -3: 28px,
    ), $scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
    get-unthemable-tokens(),
    get-color-tokens(m2-utils.$placeholder-color-config),
    get-typography-tokens(m2-utils.$placeholder-typography-config),
    get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
