//
// Design system display name: Material 3
// Design system version: v0.161
//

@function md-sys-motion-values() {
  @return (
    duration-extra-long1: 700ms,
    duration-extra-long2: 800ms,
    duration-extra-long3: 900ms,
    duration-extra-long4: 1000ms,
    duration-long1: 450ms,
    duration-long2: 500ms,
    duration-long3: 550ms,
    duration-long4: 600ms,
    duration-medium1: 250ms,
    duration-medium2: 300ms,
    duration-medium3: 350ms,
    duration-medium4: 400ms,
    duration-short1: 50ms,
    duration-short2: 100ms,
    duration-short3: 150ms,
    duration-short4: 200ms,
    easing-emphasized: cubic-bezier(0.2, 0, 0, 1),
    easing-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15),
    easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1),
    easing-legacy: cubic-bezier(0.4, 0, 0.2, 1),
    easing-legacy-accelerate: cubic-bezier(0.4, 0, 1, 1),
    easing-legacy-decelerate: cubic-bezier(0, 0, 0.2, 1),
    easing-linear: cubic-bezier(0, 0, 1, 1),
    easing-standard: cubic-bezier(0.2, 0, 0, 1),
    easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1),
    easing-standard-decelerate: cubic-bezier(0, 0, 0, 1)
  );
}
