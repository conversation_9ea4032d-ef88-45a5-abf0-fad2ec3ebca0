/*
Template Name: <PERSON>elzon - Admin & Dashboard Template
Author: Themesbrand
Website: https://Themesbrand.com/
Contact: <EMAIL>
File: invoceslist init js
*/

// list js

function getTime(params) {
    params = new Date(params);
    if (params.getHours() != null) {
        var hour = params.getHours();
        var minute = (params.getMinutes()) ? params.getMinutes() : 0;
        return hour + ":" + minute;
    }
}

function tConvert(time) {
    var d = new Date(time);
    time_s = (d.getHours() + ':' + d.getMinutes());
    var t = time_s.split(":");
    var hours = t[0];
    var minutes = t[1];
    var newformat = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12;
    minutes = minutes < 10 ? '0' + minutes : minutes;
    return (hours + ':' + minutes + ' ' + newformat);
}

var str_dt = function formatDate(date) {
    var monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];
    var d = new Date(date),
        month = '' + monthNames[(d.getMonth())],
        day = '' + d.getDate(),
        year = d.getFullYear();
    if (month.length < 2)
        month = '0' + month;
    if (day.length < 2)
        day = '0' + day;
    return [day + " " + month, year].join(', ');
};

// new Date(y, m, d + 23, 20, 0),
var date = new Date();
var d = date.getDate();
var m = date.getMonth();
var y = date.getFullYear();
var qty = 0;
var rate = 0;
var Invoices = [{
    invoice_no: '25000351',
    customer: 'Valentine Morin',
    img: 'avatar-1.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Valentine Morin',
        address: '5114 Adipiscing St. Puno United States 46782',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Quamar Payne',
        address: '534-1477 Non, Av. Bury St. Edmunds France 10846',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Reese Jacobs',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Brody Holman',
    img: 'avatar-2.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 5, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Unpaid',
    billing_address: {
        full_name: 'Brody Holman',
        address: 'P.O. Box 900 Ireland, 6694 Ullamcorper Avenue Port Pirie 37176',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Elijah Galloway',
        address: '7288 Dignissim Rd. Villa Alegre Germany 891315',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Rashawn Kuhn',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Jolie Hood',
    img: 'avatar-3.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Jolie Hood',
        address: 'Ap #957-7519 Vel, Belgium St. Diêm Điền 88188-296',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'MacKensie Peterson',
        address: '572-7561 Tempus Ave Alajuela Spain 86558',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: "Izaiah O'Kon",
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Buckminster Wong',
    img: 'avatar-4.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 8, d - 22, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Buckminster Wong',
        address: '983-8399 Egestas, Rd Spain. Penza 6596',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Emerson Riggs',
        address: '916-4370 Aliquet Avenue Nordhorn Spain 3200',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Felicity McGlynn',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Howard Lyons',
    img: '',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Refund',
    billing_address: {
        full_name: 'Howard Lyons',
        address: 'Ap #552-1397 Ac Rd Germany. Barmouth 8574',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Britanni Daniel',
        address: 'P.O. Box 998, 9293 Quisque Avenue Puerto Montt Poland 82862',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'David Gleason',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Howard Oneal',
    img: 'avatar-6.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Howard Oneal',
        address: '5642 Aliquam, Avenue Zielona Costa Rica Góra 21204',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Salvador Carney',
        address: '715-6973 Non St. Samara Peru 10513',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Reta Lang',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Jena Hall',
    img: 'avatar-7.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Cancel',
    billing_address: {
        full_name: 'Jena Hall',
        address: 'P.O. Box 332 Italy, 5256 Dignissim St. Juazeiro do Norte 646442',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Kieran Holland',
        address: '150-7530 Egestas Av. Panchià Russian Federation 16807',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Donna Hilpert',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Paki Edwards',
    img: 'avatar-8.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Paki Edwards',
        address: '2935 Senectus Av. Tvedestrand Germany 66479',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Yoshio Skinner',
        address: '101-9784 Metus Rd. Minitonas Mexico 19-154',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Evelyn Miller',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Christian Cardenas',
    img: 'avatar-1.jpg',
    email: "<EMAIL>",
    date: new Date(2022, 1, d - 20, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Christian Cardenas',
        address: '414-240 Odio. Rd Vietnam. Louisville 41715',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Linus Pitts',
        address: 'Ap #280-7347 Libero. Rd. Yurimaguas Italy 881484',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Cleora Cole',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Yoshi Guerra',
    img: 'avatar-2.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Yoshi Guerra',
        address: 'Ap #322-2982 Lacinia Road India Moss 309511',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Otto Farrell',
        address: 'Ap #827-2319 Eu Ave Bima Norway 1663',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Blaise Quigley',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Hilel Gillespie',
    img: 'avatar-3.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Hilel Gillespie',
        address: '848-2883 At Street Kalisz United Kingdom 687132',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Dacey Villarreal',
        address: '292-7088 In Road Rawalakot New Zealand 6842',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'VISA',
        card_holder_name: 'Hollie Zboncak',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Randall Stafford',
    img: 'avatar-4.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Randall Stafford',
        address: 'P.O. Box 583 Colombia, 2640 Aliquam Ave Toruń 456387',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Shana Hudson',
        address: 'Ap #973-232 Non, St. Tibet Sweden GW0R 2VR',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Kameron Barrows',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Fletcher Jones',
    img: 'avatar-5.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Fletcher Jones',
        address: 'P.O. Box 951 New Zealand, 1480 Venenatis Ave Swat 152307',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Fitzgerald Rice',
        address: '314-372 Facilisis Rd. Nancy Turkey E2K 1HY',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Gus Thiel',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Donovan Sparks',
    img: 'avatar-6.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Donovan Sparks',
        address: '176-4856 Hendrerit Av. France San Juan de Girón 58811-629',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Georgia Nixon',
        address: 'Ap #599-1431 Non, St. Cartagena del Chairá United States 2548',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Emily Stokes',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Sage Gardner',
    img: 'avatar-7.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Sage Gardner',
        address: 'Ap #193-730 Orci, Chile Street San José de Alajuela 8317',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Melinda Banks',
        address: '5778 Aliquam Road Ofena Italy 11218',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Salvador Gerlach',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Paki Grimes',
    img: 'avatar-1.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Paki Grimes',
        address: '516-3641 Tincidunt St. Pakistan Zamora de Hidalgo 6554',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Shaeleigh Wilkins',
        address: '961-3054 Integer St. Abergele United Kingdom 6746',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Marilyne Swift',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'James Diaz',
    img: 'avatar-2.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'James Diaz',
        address: 'Ap #160-8536 Ante St Colombia. Santa Coloma de Gramenet 19475',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Julian Tanner',
        address: '630-5275 Quis Street Kraków Canada E39 0RE',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Kraig Prohaska',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Karen Monroe',
    img: 'avatar-3.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Karen Monroe',
        address: '486-3233 Quis Road Burnie Costa Rica 82926',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Jescie Keller',
        address: '256-3596 Fermentum Road Salzburg United States 86-910',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Domenic Kassulke',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Vincent Weeks',
    img: 'avatar-4.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Vincent Weeks',
        address: '128-7206 Sit Street Bathurst Indonesia 812326',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Jonah Hayden',
        address: 'Ap #315-5686 Luctus. Rd. Samaniego Canada 482995',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Abner Muller',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Miriam Dickson',
    img: 'avatar-5.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Miriam Dickson',
        address: '1747 Dui, Ave Springdale Russian Federation 67155',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Eaton Buckley',
        address: '846-7108 Orci. Road Ukkel India 624087',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Elyse Green',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Ashton Head',
    img: '',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Ashton Head',
        address: '735-6864 Mauris Ave Linz South Korea 39964',
        phone: '(*************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Lani Ashley',
        address: 'P.O. Box 451, 696 Metus Avenue Jaboatão dos Guararapes Colombia 391846',
        phone: '(*************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Wilhelmine Cummerata',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}, {
    invoice_no: '********',
    customer: 'Linus Martin',
    img: 'avatar-2.jpg',
    email: "<EMAIL>",
    date: new Date(2021, 3, d - 23, 21, 58),
    invoice_amount: 875,
    status: 'Paid',
    billing_address: {
        full_name: 'Linus Martin',
        address: '907-233 Vehicula. Road Vietnam Vienna 8231',
        phone: '**************',
        tax: '*********'
    },
    shipping_address: {
        full_name: 'Yuri Allison',
        address: 'Ap #769-2743 Pede. Road Gönen Spain 83472-82897',
        phone: '**************',
        tax: '*********'
    },
    prducts: [{
            product_name: 'Sweatshirt for Men (Pink)',
            product_details: 'Graphic Print Men & Women Sweatshirt',
            rates: (rate = 119.99),
            quantity: (qty = 2),
            amount: (rate * qty)
        },
        {
            product_name: 'Noise NoiseFit Endure Smart Watch',
            product_details: '32.5mm (1.28 Inch) TFT Color Touch Display',
            rates: (rate = 94.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        },
        {
            product_name: '350 ml Glass Grocery Container',
            product_details: 'Glass Grocery Container (Pack of 3, White)',
            rates: (rate = 24.99),
            quantity: (qty = 1),
            amount: (rate * qty)
        }
    ],
    payment_details: {
        payment_method: 'MasterCard',
        card_holder_name: 'Tania Price',
        card_number: '****************',
        total_amount: 415.96
    },
    company_details: {
        legal_registration_no: "987654",
        email: '<EMAIL>',
        website: 'www.themesbrand.com',
        contact_no: '0*********',
        address: 'California, United States',
        zip_code: '90201'
    },
    order_summary: {
        sub_total: 359.96,
        estimated_tex: 44.99,
        discount: 53.99,
        shipping_charge: 65.00,
        total_amount: 415.96,
    },
    notes: 'All accounts are to be paid within 7 days from receipt of invoice. To be paid by cheque or credit card or direct payment online. If account is not paid within 7 days the credits details supplied as confirmation of work undertaken will be charged the agreed quoted fee noted above.'
}];


if ((sessionStorage.getItem("invoices-list") === null) && (sessionStorage.getItem("new_data_object") === null)) {
    Invoices = Invoices;
} else if ((sessionStorage.getItem("invoices-list") === null) && (sessionStorage.getItem("new_data_object") !== null)) {
    var invoice_new_obj = JSON.parse(sessionStorage.getItem("new_data_object"));
    Invoices.push(invoice_new_obj);
    sessionStorage.removeItem("new_data_object");
} else {
    Invoices = [];
    Invoices = JSON.parse(sessionStorage.getItem("invoices-list"));
    if (sessionStorage.getItem("new_data_object") !== null) {
        var invoice_new_obj = JSON.parse(sessionStorage.getItem("new_data_object"));
        Invoices.push(invoice_new_obj);
        sessionStorage.removeItem("new_data_object");
    }
    sessionStorage.removeItem("invoices-list");
}

//ist form-check-all
Array.from(Invoices).forEach(function (raw) {
    let badge;
    switch (raw.status) {
        case 'Paid':
            badge = "success";
            break;
        case 'Refund':
            badge = "primary";
            break;
        case 'Unpaid':
            badge = "warning";
            break;
        case 'Cancel':
            badge = "danger";
    }
    if (raw.img) {
        var avatar_ = `<img src='assets/images/users/` + raw.img + `' alt='' class='avatar-xs rounded-circle me-2'>`;
    } else {
        var avtar_title = (raw.customer).split(" ");
        var letters = null;
        if (avtar_title.length >= 2) {
            var first_letter = avtar_title[0].slice(0, 1);
            var secont_letter = avtar_title[1].slice(0, 1);
            letters = first_letter + secont_letter
        } else {
            var first_letter = avtar_title[0].slice(0, 1);
            letters = first_letter
        }
        var avatar_ = `<div class="flex-shrink-0 avatar-xs me-2"><div class="avatar-title bg-success-subtle text-success rounded-circle fs-13">` + letters + `</div></div>`;
    }

    var tableRawData = `<tr>
                <th scope="row">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="chk_child" value="#VL${raw.invoice_no}">
                    </div>
                </th>
                <td class="id"><a href="javascript:void(0);" onclick="ViewInvoice(this);" data-id="` + raw.invoice_no + `" class="fw-medium link-primary">#VL` + raw.invoice_no + `</a></td>
                <td class="customer_name">
                    <div class="d-flex align-items-center">
                        ` + avatar_ + raw.customer + `
                    </div>
                </td>
                <td class="email">` + raw.email + `</td>
                <td class="country">USA</td>
                <td class="date">` + str_dt(raw.date) + ` <small class="text-muted">` + tConvert(raw.date) + `</small></td>
                <td class="invoice_amount">$` + (raw.invoice_amount) + `</td>
                <td class="status"><span class="badge bg-` + badge + `-subtle text-` + badge + ` text-uppercase">` + raw.status + `</span>
                </td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-more-fill align-middle"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><button class="dropdown-item" href="javascript:void(0);" onclick="ViewInvoice(this);" data-id="` + raw.invoice_no + `" ><i class="ri-eye-fill align-bottom me-2 text-muted"></i>
                                    View</button></li>
                            <li><button class="dropdown-item" href="javascript:void(0);" onclick="EditInvoice(this);" data-id="` + raw.invoice_no + `"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i>
                                    Edit</button></li>
                            <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-line align-bottom me-2 text-muted"></i>
                                    Download</a></li>
                            <li class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item remove-item-btn" data-bs-toggle="modal" href="#deleteOrder">
                                    <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i>
                                    Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>`;

    document.getElementById('invoice-list-data').innerHTML += tableRawData;
});

document.addEventListener("DOMContentLoaded", function () {
    var genericExamples = document.querySelectorAll('[data-plugin="choices"]');
    Array.from(genericExamples).forEach(function (genericExamp) {
        var element = genericExamp;
        new Choices(element, {
            placeholderValue: "This is a placeholder set in the config",
            searchPlaceholderValue: "Search results here",
        });
    });
});

flatpickr("#datepicker-range", {
    mode: "range",
    dateFormat: "d M, Y",
});

flatpickr("#date-field", {
    dateFormat: "d M, Y",
});

var checkAll = document.getElementById("checkAll");
if (checkAll) {
  checkAll.onclick = function () {
    var checkboxes = document.querySelectorAll('.form-check-all input[type="checkbox"]');
    var checkedCount = document.querySelectorAll('.form-check-all input[type="checkbox"]:checked').length;
    for (var i = 0; i < checkboxes.length; i++) {
      checkboxes[i].checked = this.checked;
      if (checkboxes[i].checked) {
          checkboxes[i].closest("tr").classList.add("table-active");
      } else {
          checkboxes[i].closest("tr").classList.remove("table-active");
      }
    }

    (checkedCount > 0) ? document.getElementById("remove-actions").style.display = 'none' : document.getElementById("remove-actions").style.display = 'block';
  };
}

var perPage = 8;

//Table
var options = {
    valueNames: [
        "id",
        "customer_name",
        "email",
        "country",
        "date",
        "invoice_amount",
        "status",
    ],
    page: perPage,
    pagination: true,
    plugins: [
        ListPagination({
            left: 2,
            right: 2,
        }),
    ],
};

// Init list
var invoiceList = new List("invoiceList", options).on(
    "updated",
    function (list) {
        list.matchingItems.length == 0 ?
            (document.getElementsByClassName("noresult")[0].style.display = "block") :
            (document.getElementsByClassName("noresult")[0].style.display = "none");
        var isFirst = list.i == 1;
        var isLast = list.i > list.matchingItems.length - list.page;
        // make the Prev and Nex buttons disabled on first and last pages accordingly
        document.querySelector(".pagination-prev.disabled") ?
            document.querySelector(".pagination-prev.disabled").classList.remove("disabled") : "";
        document.querySelector(".pagination-next.disabled") ?
            document.querySelector(".pagination-next.disabled").classList.remove("disabled") : "";
        if (isFirst) {
            document.querySelector(".pagination-prev").classList.add("disabled");
        }
        if (isLast) {
            document.querySelector(".pagination-next").classList.add("disabled");
        }
        if (list.matchingItems.length <= perPage) {
            document.querySelector(".pagination-wrap").style.display = "none";
        } else {
            document.querySelector(".pagination-wrap").style.display = "flex";
        }
        if (list.matchingItems.length == perPage) {
            document.querySelector(".pagination.listjs-pagination").firstElementChild.children[0].click();
        }
        if (list.matchingItems.length > 0) {
            document.getElementsByClassName("noresult")[0].style.display = "none";
        } else {
            document.getElementsByClassName("noresult")[0].style.display = "block";
        }
    }
);

isCount = new DOMParser().parseFromString(
    invoiceList.items.slice(-1)[0]._values.id,
    "text/html"
);

var isValue = isCount.body.firstElementChild.innerHTML;

var idField = document.getElementById("orderId"),
    customerNameField = document.getElementById("customername-field"),
    emailField = document.getElementById("email-field"),
    dateField = document.getElementById("date-field"),
    countryField = document.getElementById("country-field"),
    statusField = document.getElementById("delivered-status"),
    addBtn = document.getElementById("add-btn"),
    editBtn = document.getElementById("edit-btn"),
    removeBtns = document.getElementsByClassName("remove-item-btn"),
    editBtns = document.getElementsByClassName("edit-item-btn");
refreshCallbacks();
filterContact("All");

function filterContact(isValue) {
    var values_status = isValue;
    invoiceList.filter(function (data) {
        var statusFilter = false;
        matchData = new DOMParser().parseFromString(
            data.values().status,
            "text/html"
        );
        var status = matchData.body.firstElementChild.innerHTML;
        if (status == "All" || values_status == "All") {
            statusFilter = true;
        } else {
            statusFilter = status == values_status;
        }
        return statusFilter;
    });

    invoiceList.update();
}

function updateList() {
    var values_status = document.querySelector("input[name=status]:checked").value;
    data = userList.filter(function (item) {
        var statusFilter = false;
        if (values_status == "All") {
            statusFilter = true;
        } else {
            statusFilter = item.values().sts == values_status;
        }
        return statusFilter;
    });
    userList.update();
}

var table = document.getElementById("invoiceTable");
// save all tr
var tr = table.getElementsByTagName("tr");
var trlist = table.querySelectorAll(".list tr");

function SearchData() {
    var isstatus = document.getElementById("idStatus").value;
    var pickerVal = document.getElementById("datepicker-range").value;

    var date1 = pickerVal.split(" to ")[0];
    var date2 = pickerVal.split(" to ")[1];

    invoiceList.filter(function (data) {
        matchData = new DOMParser().parseFromString(
            data.values().status,
            "text/html"
        );
        var status = matchData.body.firstElementChild.innerHTML;
        var statusFilter = false;
        var dateFilter = false;

        if (status == "all" || isstatus == "all") {
            statusFilter = true;
        } else {
            statusFilter = status == isstatus;
        }

        if (new Date(data.values().date.slice(0, 12)) >= new Date(date1) && new Date(data.values().date.slice(0, 12)) <= new Date(date2)) {
            dateFilter = true;
        } else {
            dateFilter = false;
        }

        if (statusFilter && dateFilter) {
            return statusFilter && dateFilter;
        } else if (statusFilter && pickerVal == "") {
            return statusFilter;
        } else if (dateFilter && pickerVal == "") {
            return dateFilter;
        }
    });
    invoiceList.update();
}

function ischeckboxcheck() {
    Array.from(document.getElementsByName("chk_child")).forEach(function (x) {
        x.addEventListener("change", function (e) {
            if (x.checked == true) {
                e.target.closest("tr").classList.add("table-active");
            } else {
                e.target.closest("tr").classList.remove("table-active");
            }
  
            var checkedCount = document.querySelectorAll('[name="chk_child"]:checked').length;
            if (e.target.closest("tr").classList.contains("table-active")) {
                (checkedCount > 0) ? document.getElementById("remove-actions").style.display = 'block': document.getElementById("remove-actions").style.display = 'none';
            } else {
                (checkedCount > 0) ? document.getElementById("remove-actions").style.display = 'block': document.getElementById("remove-actions").style.display = 'none';
            }
        });
    });
}

function refreshCallbacks() {
    Array.from(removeBtns).forEach(function (btn) {
        btn.addEventListener("click", function (e) {
            e.target.closest("tr").children[1].innerText;
            itemId = e.target.closest("tr").children[1].innerText;
            var itemValues = invoiceList.get({
                id: itemId,
            });

            Array.from(itemValues).forEach(function (x) {
                deleteid = new DOMParser().parseFromString(x._values.id, "text/html");

                var isElem = deleteid.body.firstElementChild;
                var isdeleteid = deleteid.body.firstElementChild.innerHTML;
                if (isdeleteid == itemId) {
                    document.getElementById("delete-record").addEventListener("click", function () {
                        invoiceList.remove("id", isElem.outerHTML);
                        document.getElementById("deleteRecord-close").click();
                    });
                }
            });
        });
    });
}

document.querySelector("#invoiceList").addEventListener("click", function () {
    ischeckboxcheck();
});

function clearFields() {
    customerNameField.value = "";
    emailField.value = "";
    dateField.value = "";
    countryField.value = "";
}

document.querySelector(".pagination-next").addEventListener("click", function () {
    document.querySelector(".pagination.listjs-pagination") ?
        document.querySelector(".pagination.listjs-pagination").querySelector(".active") ?
        document.querySelector(".pagination.listjs-pagination").querySelector(".active").nextElementSibling.children[0].click() : "" : "";
});

document.querySelector(".pagination-prev").addEventListener("click", function () {
    document.querySelector(".pagination.listjs-pagination") ?
        document.querySelector(".pagination.listjs-pagination").querySelector(".active") ?
        document.querySelector(".pagination.listjs-pagination").querySelector(".active").previousSibling.children[0].click() : "" : "";
});

function ViewInvoice(data) {
    var invoice_no = data.getAttribute('data-id');
    sessionStorage.setItem("invoices-list", JSON.stringify(Invoices));
    sessionStorage.setItem("option", "view-invoice");
    sessionStorage.setItem("invoice_no", invoice_no);
    window.location.assign("apps-invoices-details.html")
}

function EditInvoice(data) {
    var invoice_no = data.getAttribute('data-id');
    sessionStorage.setItem("invoices-list", JSON.stringify(Invoices));
    sessionStorage.setItem("option", "edit-invoice");
    sessionStorage.setItem("invoice_no", invoice_no);
    window.location.assign("apps-invoices-create.html")
}

// Delete Multiple Records
function deleteMultiple() {
    ids_array = [];
    var items = document.getElementsByName('chk_child');
    for (i = 0; i < items.length; i++) {
        if (items[i].checked == true) {
            ids_array.push(items[i].value);
        }
    }
    
    if (typeof ids_array !== 'undefined' && ids_array.length > 0) {
        Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonClass: 'btn btn-primary w-xs me-2 mt-2',
            cancelButtonClass: 'btn btn-danger w-xs mt-2',
            confirmButtonText: "Yes, delete it!",
            buttonsStyling: false,
            showCloseButton: true
        }).then(function (result) {
            if (result.value) {
                for (i = 0; i < ids_array.length; i++) {
                    invoiceList.remove("id", `<a href="javascript:void(0);" onclick="ViewInvoice(this);" data-id="` + ids_array[i].slice(3) + `" class="fw-medium link-primary">${ids_array[i]}</a>`);
                }
                document.getElementById("remove-actions").style.display = 'none';
                document.getElementById("checkAll").checked = false;
                Swal.fire({
                    title: 'Deleted!',
                    text: 'Your data has been deleted.',
                    icon: 'success',
                    confirmButtonClass: 'btn btn-info w-xs mt-2',
                    buttonsStyling: false
                });
            }
        });
    } else {
        Swal.fire({
            title: 'Please select at least one checkbox',
            confirmButtonClass: 'btn btn-info',
            buttonsStyling: false,
            showCloseButton: true
        });
    }
}