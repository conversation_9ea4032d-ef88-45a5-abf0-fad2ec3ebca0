import * as i0 from '@angular/core';
import { NgModule } from '@angular/core';
import { M as MatCommonModule } from './common-module-cKSwHniA.mjs';
import { M as MatRipple } from './ripple-BYgV4oZC.mjs';

class MatRippleModule {
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "20.0.0", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });
    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "20.0.0", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatRipple], exports: [MatRipple, MatCommonModule] });
    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "20.0.0", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatCommonModule] });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "20.0.0", ngImport: i0, type: MatRippleModule, decorators: [{
            type: NgModule,
            args: [{
                    imports: [MatCommonModule, MatRipple],
                    exports: [MatRipple, MatCommonModule],
                }]
        }] });

export { MatRippleModule as M };
//# sourceMappingURL=index-BFRo2fUq.mjs.map
