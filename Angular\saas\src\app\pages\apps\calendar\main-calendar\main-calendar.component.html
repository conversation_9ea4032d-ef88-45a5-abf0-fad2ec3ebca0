<!-- Start Breadcrumbs -->
<app-breadcrumbs title="CALENDAR" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="row">
    <div class="col-12">
        <div class="row">
            <div class="col-xl-12">
                <div class="card card-h-100">
                    <div class="card-body">
                        <full-calendar [options]="calendarOptions"></full-calendar>
                        <!-- <div id="calendar"></div> -->
                    </div>
                </div>
            </div><!-- end col -->
        </div>
        <!--end row-->

        <div style='clear:both'></div>

        <!-- Add New Event MODAL -->
        <ng-template #modalShow let-modal>
            <div id="event-modal">
                <div class="modal-content border-0">
                    <div class="modal-header p-3 bg-info-subtle">
                        <h5 class="modal-title" id="modal-title">Add Event</h5>
                        <button type="button" class="btn-close" (click)="modal.dismiss('Cross click')"></button>
                    </div>
                    <div class="modal-body p-4">
                        <form [formGroup]="formData" (ngSubmit)="saveEvent()" class="needs-validation" name="event-form" id="form-event" novalidate>
                            @if(isEditMode){
                            <div class="text-end">
                                <a href="javascript:void(0);" class="btn btn-sm btn-soft-primary" id="edit-event-btn" data-id="edit-event" (click)="showeditEvent()" role="button">Edit</a>
                            </div>
                        }
                        @if(editEvent){
                            <div class="event-details">
                                <div class="d-flex mb-2">
                                    <div class="flex-grow-1 d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <i class="ri-calendar-event-line text-muted fs-lg"></i>
                                        </div>
                                        @if(editEvent.start){
                                        <div class="flex-grow-1">
                                            <h6 class="d-block fw-semibold mb-0" id="event-start-date-tag">{{editEvent.start | date: 'dd MMMM,yyyy'}}</h6>
                                        </div>
                                    }
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="ri-time-line text-muted fs-lg"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        @if(editEvent.end){
                                        <h6 class="d-block fw-semibold mb-0">
                                            <span id="event-timepicker1-tag"></span> {{editEvent.start | date: 'h:mm a'}} - {{editEvent.end | date:'h:mm a'}} <span id="event-timepicker2-tag"></span>
                                        </h6>
                                        }@else{
                                            <h6 class="d-block fw-semibold mb-0">
                                                <span id="event-timepicker1-tag"></span> - <span id="event-timepicker2-tag"></span>
                                            </h6>
                                        }
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="ri-map-pin-line text-muted fs-lg"></i>
                                    </div>
                                    @if(editEvent.extendedProps){
                                    <div class="flex-grow-1">
                                        <h6 class="d-block fw-semibold mb-0"> <span id="event-location-tag">{{editEvent.extendedProps['location']}}</span></h6>
                                    </div>
                                }
                                </div>
                                <div class="d-flex mb-3">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="ri-discuss-line text-muted fs-lg"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="d-block text-muted mb-0" id="event-description-tag">{{editEvent.extendedProps['description']}}</p>
                                    </div>
                                </div>
                            </div>
                        }
                            <div class="row event-form">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Event Name</label>
                                        <input class="form-control" placeholder="Enter event name" type="text" formControlName="title" name="title" id="event-title" required value="">
                                        <div class="invalid-feedback">Please provide a valid event name</div>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Event Date</label>
                                        <div class="input-group">
                                            <input type="text" id="event-start-date" class="form-control flatpickr flatpickr-input" mwlFlatpickr [altInput]="true" altFormat="d/m/Y" [convertModelValue]="true" formControlName="date" placeholder="Select date" readonly required>
                                            <span class="input-group-text"><i class="ri-calendar-event-line"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-12" id="event-time">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="mb-3">
                                                <label class="form-label">Start Time</label>
                                                <div class="input-group">
                                                    <input id="timepicker1" type="text" mwlFlatpickr [noCalendar]="true" [enableTime]="true" [dateFormat]="'H:i'" formControlName="start" class="form-control flatpickr flatpickr-input" placeholder="Select start time" readonly>
                                                    <span class="input-group-text"><i class="ri-time-line"></i></span>
                                                </div>
                                            </div>
                                        </div><!--end col-->
                                        <div class="col-6">
                                            <div class="mb-3">
                                                <label class="form-label">End Time</label>
                                                <div class="input-group">
                                                    <input id="timepicker2" type="text" mwlFlatpickr [noCalendar]="true" [enableTime]="true" [dateFormat]="'H:i'" formControlName="end" class="form-control flatpickr flatpickr-input" placeholder="Select end time" readonly>
                                                    <span class="input-group-text"><i class="ri-time-line"></i></span>
                                                </div>
                                            </div>
                                        </div><!--END col-->
                                    </div><!--end row-->
                                </div>
                                <!--end col-->
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="event-location" class="form-label">Location</label>
                                        <div>
                                            <input type="text" class="form-control" formControlName="location" name="event-location" id="event-location" placeholder="Event location">
                                        </div>
                                    </div>
                                </div><!--end col-->
                                <input type="hidden" id="eventid" name="eventid" value="">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Description</label>
                                        <textarea class="form-control" id="event-description" placeholder="Enter a description" rows="3" formControlName="description" spellcheck="false"></textarea>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Type</label>
                                        <select class="form-select" formControlName="category" name="category" id="event-category" required>
                                            <option value="bg-danger-subtle">Danger</option>
                                            <option value="bg-success-subtle">Success</option>
                                            <option value="bg-primary-subtle">Primary</option>
                                            <option value="bg-info-subtle">Info</option>
                                            <option value="bg-dark-subtle">Dark</option>
                                            <option value="bg-warning-subtle">Warning</option>
                                        </select>
                                        <div class="invalid-feedback">Please select a valid event category</div>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                            <div class="hstack gap-2 justify-content-end">
                                <button type="button" class="btn btn-soft-danger" id="btn-delete-event" (click)="deleteEventData()"><i class="ri-close-line align-bottom"></i> Delete</button>
                                <button type="submit" class="btn btn-success" id="btn-save-event">Add Event</button>
                            </div>
                        </form>
                    </div>
                </div> <!-- end modal-content-->
            </div> <!-- end modal dialog-->s
        </ng-template>
        <!-- end modal-->

        <!-- Edit event modal -->
        <ng-template #editmodalShow let-modal>
            <div class="modal-header p-3 bg-info-subtle ">
                <h5 class="modal-title" id="modal-title">All Day Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true" (click)="modal.dismiss('Cross click')"></button>
            </div>
            <div class="modal-body p-4">
                <form [formGroup]="formEditData" (ngSubmit)="editEventSave()">
                    <div class="row event-form">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Type</label>
                                <select class="form-control" name="ecategory" formControlName="editCategory" required>
                                    @for(option of category; track $index){
                                    <option value="{{ option.value }}">
                                        {{ option.name }}
                                    </option>
                                }
                                </select>
                            </div>
                        </div>
                        <!--end col-->
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Event Name</label>
                                <input class="form-control" placeholder="Insert Event Name" type="text" name="editTitle" formControlName="editTitle" />
                            </div>
                        </div>
                        <!--end col-->
                        <div class="col-12">
                            <div class="mb-3">
                                <label>Event Date</label>
                                <div class="input-group">
                                    <input class="form-control flatpickr-input" type="text" mwlFlatpickr name="editDate" formControlName="editDate" [altInput]="true" [convertModelValue]="true" placeholder="Select date">
                                </div>
                            </div>
                        </div>
                        <!--end col-->
                        <div class="col-12" id="event-time">
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <input class="form-control flatpickr-input" type="text" placeholder="Select start time" mwlFlatpickr [noCalendar]="true" name="editStart" formControlName="editStart" [enableTime]="true" [dateFormat]="'H:i'">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">End Time</label>
                                        <div class="input-group">
                                            <input class="form-control flatpickr-input" type="text" placeholder="Select end time" mwlFlatpickr [noCalendar]="true" name="editEnd" formControlName="editEnd" [enableTime]="true" [dateFormat]="'H:i'">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end col-->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="event-location">Location</label>
                                <div>
                                    <input type="text" class="form-control" name="editlocation" formControlName="editlocation" placeholder="Event location">
                                </div>
                            </div>
                        </div>
                        <!--end col-->
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="editDescription" placeholder="Enter a description" formControlName="editDescription" rows="3" spellcheck="false"></textarea>
                            </div>
                        </div>
                        <!--end col-->
                    </div>
                    <div class="hstack gap-2 justify-content-end">
                        <button type="button" class="btn btn-danger-subtle" id="btn-delete-event" (click)="confirm()"><i class="ri-close-line align-bottom"></i> Delete</button>
                        <button type="submit" class="btn btn-success" id="btn-save-event">Update Event</button>
                    </div>
                </form>
            </div>
        </ng-template>
        <!-- end modal-->
    </div>
</div> <!-- end row-->