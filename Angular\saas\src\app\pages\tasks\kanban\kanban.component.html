<!-- Start Breadcrumbs -->
<app-breadcrumbs title="Kanban Board" [breadcrumbItems]="breadCrumbItems"></app-breadcrumbs>
<!-- End Breadcrumbs -->

<div class="card">
    <div class="card-body">
        <div class="row g-2">
            <div class="col-lg-auto">
                <div class="hstack gap-2">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createboardModal"
                        (click)="openModal(content)"><i class="ri-add-line align-bottom me-1"></i> Create Board</button>
                </div>
            </div><!--end col-->
            <div class="col-lg-3 col-auto">
                <div class="search-box">
                    <input type="text" class="form-control search" placeholder="Search for project, tasks..."
                        [(ngModel)]="searchTerm">
                    <i class="ri-search-line search-icon"></i>
                </div>
            </div>
            <div class="col-auto ms-sm-auto">
                <div class="avatar-group" id="newMembar">
                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Nancy" placement="top">
                        <img src="assets/images/users/avatar-5.jpg" alt="" class="rounded-circle avatar-xs">
                    </a>
                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Frank" placement="top">
                        <img src="assets/images/users/avatar-3.jpg" alt="" class="rounded-circle avatar-xs">
                    </a>
                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Tonya" placement="top">
                        <img src="assets/images/users/avatar-10.jpg" alt="" class="rounded-circle avatar-xs">
                    </a>
                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Thomas" placement="top">
                        <img src="assets/images/users/avatar-8.jpg" alt="" class="rounded-circle avatar-xs">
                    </a>
                    <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="Herbert" placement="top">
                        <img src="assets/images/users/avatar-2.jpg" alt="" class="rounded-circle avatar-xs">
                    </a>
                    <a data-bs-toggle="modal" class="avatar-group-item" (click)="openMemberModal(membercontent)">
                        <div class="avatar-xs">
                            <div class="avatar-title rounded-circle">
                                +
                            </div>
                        </div>
                    </a>
                </div>
            </div><!--end col-->
        </div><!--end row-->
    </div><!--end card-body-->
</div><!--end card-->

<div class="tasks-board mb-3" id="kanbanboard">
    <div class="tasks-list">
        <div class="d-flex mb-3">
            <div class="flex-grow-1">
                <h6 class="fs-13 text-uppercase mb-0">Unassigned <small
                        class="badge bg-success align-bottom ms-1">2</small></h6>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown" ngbDropdown>
                    <a class="text-reset dropdown-btn arrow-none" href="javascript:void(0);" data-bs-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" ngbDropdownToggle>
                        <span class="fw-medium text-muted fs-13">Priority<i
                                class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                        <a class="dropdown-item" href="javascript:void(0);">Priority</a>
                        <a class="dropdown-item" href="javascript:void(0);">Date Added</a>
                    </div>
                </div>
            </div>
        </div>
        <ngx-simplebar class="tasklist-content">
            <div id="unassigned-task" class="tasks" [dndDropzone] dndEffectAllowed="move"
                (dndDrop)="onDrop($event, unassignedTasks,'Progress-task')">
                <div class="dndPlaceholder" dndPlaceholderRef></div>
                @for(task of unassignedTasks; track $index){
                <ng-container>
                    <div [dndDraggable]="task" dndEffectAllowed="move" (dndMoved)="onDragged(task, unassignedTasks)">
                        <ng-template [ngTemplateOutlet]="TaskContent" [ngTemplateOutletContext]="{task:task}">
                        </ng-template>
                    </div>
                </ng-container>
            }
            </div>
        </ngx-simplebar>
        <div class="my-3">
            <button class="btn btn-soft-info w-100" data-bs-toggle="modal" data-bs-target="#creatertaskModal"
                (click)="openAddModal(addcontent)">Add More</button>
        </div>
    </div><!--end tasks-list-->
    <div class="tasks-list">
        <div class="d-flex mb-3">
            <div class="flex-grow-1">
                <h6 class="fs-13 text-uppercase mb-0">To Do <small
                        class="badge bg-secondary align-bottom ms-1">2</small></h6>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown" ngbDropdown>
                    <a class="text-reset dropdown-btn arrow-none" href="javascript:void(0);" data-bs-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" ngbDropdownToggle>
                        <span class="fw-medium text-muted fs-13">Priority<i
                                class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                        <a class="dropdown-item" href="javascript:void(0);">Priority</a>
                        <a class="dropdown-item" href="javascript:void(0);">Date Added</a>
                    </div>
                </div>
            </div>
        </div>
        <ngx-simplebar class="tasklist-content">
            <div id="inprogress-task" class="tasks" [dndDropzone] dndEffectAllowed="move"
                (dndDrop)="onDrop($event, todoTasks,'Progress-task')">
                <div class="dndPlaceholder" dndPlaceholderRef></div>
                @for(task of todoTasks; track $index){
                <ng-container>
                    <div [dndDraggable]="task" dndEffectAllowed="move" (dndMoved)="onDragged(task, todoTasks)">
                        <ng-template [ngTemplateOutlet]="TaskContent" [ngTemplateOutletContext]="{task:task}">
                        </ng-template>
                    </div>
                </ng-container>
            }
            </div>
        </ngx-simplebar>
        <div class="my-3">
            <button class="btn btn-soft-info w-100" data-bs-toggle="modal" data-bs-target="#creatertaskModal"
                (click)="openAddModal(addcontent)">Add More</button>
        </div>
    </div><!--end tasks-list-->
    <div class="tasks-list">
        <div class="d-flex mb-3">
            <div class="flex-grow-1">
                <h6 class="fs-13 text-uppercase mb-0">Inprogress <small
                        class="badge bg-warning align-bottom ms-1">2</small></h6>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown" ngbDropdown>
                    <a class="text-reset dropdown-btn arrow-none" href="javascript:void(0);" data-bs-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" ngbDropdownToggle>
                        <span class="fw-medium text-muted fs-12">Priority<i
                                class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                        <a class="dropdown-item" href="javascript:void(0);">Priority</a>
                        <a class="dropdown-item" href="javascript:void(0);">Date Added</a>
                    </div>
                </div>
            </div>
        </div>
        <ngx-simplebar class="tasklist-content">
            <div id="inprogress-task" class="tasks" [dndDropzone] dndEffectAllowed="move"
                (dndDrop)="onDrop($event, inprogressTasks,'Progress-task')">
                <div class="dndPlaceholder" dndPlaceholderRef></div>
                @for(task of inprogressTasks; track $index){
                <ng-container>
                    <div [dndDraggable]="task" dndEffectAllowed="move" (dndMoved)="onDragged(task, inprogressTasks)">
                        <ng-template [ngTemplateOutlet]="TaskInProgressContent" [ngTemplateOutletContext]="{task:task}">
                        </ng-template>
                    </div>
                </ng-container>
            }
            </div>
        </ngx-simplebar>
        <div class="my-3">
            <button class="btn btn-soft-info w-100" data-bs-toggle="modal" data-bs-target="#creatertaskModal"
                (click)="openAddModal(addcontent)">Add More</button>
        </div>
    </div><!--end tasks-list-->
    <div class="tasks-list">
        <div class="d-flex mb-3">
            <div class="flex-grow-1">
                <h6 class="fs-13 text-uppercase mb-0">In Reviews <small
                        class="badge bg-info align-bottom ms-1">3</small></h6>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown" ngbDropdown>
                    <a class="text-reset dropdown-btn arrow-none" href="javascript:void(0);" data-bs-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" ngbDropdownToggle>
                        <span class="fw-medium text-muted fs-12">Priority<i
                                class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                        <a class="dropdown-item" href="javascript:void(0);">Priority</a>
                        <a class="dropdown-item" href="javascript:void(0);">Date Added</a>
                    </div>
                </div>
            </div>
        </div>
        <ngx-simplebar class="tasklist-content">
            <div id="inprogress-task" class="tasks" [dndDropzone] dndEffectAllowed="move"
                (dndDrop)="onDrop($event, reviewsTasks,'Progress-task')">
                <div class="dndPlaceholder" dndPlaceholderRef></div>
                @for(task of reviewsTasks; track $index){
                <ng-container>
                    <div [dndDraggable]="task" dndEffectAllowed="move" (dndMoved)="onDragged(task, reviewsTasks)">
                        <ng-template [ngTemplateOutlet]="TaskInReviewsContent" [ngTemplateOutletContext]="{task:task}">
                        </ng-template>
                    </div>
                </ng-container>
            }
            </div>
        </ngx-simplebar>
        <div class="my-3">
            <button class="btn btn-soft-info w-100" data-bs-toggle="modal" data-bs-target="#creatertaskModal"
                (click)="openAddModal(addcontent)">Add More</button>
        </div>
    </div><!--end tasks-list-->
    <div class="tasks-list">
        <div class="d-flex mb-3">
            <div class="flex-grow-1">
                <h6 class="fs-13 text-uppercase mb-0">Completed <small
                        class="badge bg-success align-bottom ms-1">1</small></h6>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown" ngbDropdown>
                    <a class="text-reset dropdown-btn arrow-none" href="javascript:void(0);" data-bs-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" ngbDropdownToggle>
                        <span class="fw-medium text-muted fs-12">Priority<i
                                class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                        <a class="dropdown-item" href="javascript:void(0);">Priority</a>
                        <a class="dropdown-item" href="javascript:void(0);">Date Added</a>
                    </div>
                </div>
            </div>
        </div>
        <ngx-simplebar class="tasklist-content">
            <div id="inprogress-task" class="tasks" [dndDropzone] dndEffectAllowed="move"
                (dndDrop)="onDrop($event, completedTasks,'Progress-task')">
                <div class="dndPlaceholder" dndPlaceholderRef></div>
                @for(task of completedTasks; track $index){
                <ng-container>
                    <div [dndDraggable]="task" dndEffectAllowed="move" (dndMoved)="onDragged(task, completedTasks)">
                        <ng-template [ngTemplateOutlet]="TaskContent" [ngTemplateOutletContext]="{task:task}">
                        </ng-template>
                    </div>
                </ng-container>
            }
            </div>
        </ngx-simplebar>
        <div class="my-3">
            <button class="btn btn-soft-info w-100" data-bs-toggle="modal" data-bs-target="#creatertaskModal"
                (click)="openAddModal(addcontent)">Add More</button>
        </div>
    </div><!--end tasks-list-->
    <div class="tasks-list">
        <div class="d-flex mb-3">
            <div class="flex-grow-1">
                <h6 class="fs-13 text-uppercase mb-0">new <small class="badge bg-success align-bottom ms-1">1</small>
                </h6>
            </div>
            <div class="flex-shrink-0">
                <div class="dropdown card-header-dropdown" ngbDropdown>
                    <a class="text-reset dropdown-btn arrow-none" href="javascript:void(0);" data-bs-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" ngbDropdownToggle>
                        <span class="fw-medium text-muted fs-12">Priority<i
                                class="mdi mdi-chevron-down ms-1"></i></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
                        <a class="dropdown-item" href="javascript:void(0);">Priority</a>
                        <a class="dropdown-item" href="javascript:void(0);">Date Added</a>
                    </div>
                </div>
            </div>
        </div>
        <ngx-simplebar class="tasklist-content">
            <div class="card tasks-box">
                <div class="card-body">
                    <div class="d-flex mb-2">
                        <a href="javascript:void(0)" class="text-muted fw-medium fs-14 flex-grow-1">#VL5287</a>
                        <div class="dropdown" ngbDropdown>
                            <a href="javascript:void(0);" class="text-muted arrow-none" id="dropdownMenuLink2"
                                data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle><i
                                    class="ri-more-fill"></i></a>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink2" ngbDropdownMenu>
                                <li><a class="dropdown-item" routerLink="/tasks/details"><i
                                            class="ri-eye-fill align-bottom me-2 text-muted float-start"></i> View</a>
                                </li>
                                <li><a class="dropdown-item" href="javascript:void(0);"><i
                                            class="ri-edit-2-line align-bottom me-2 text-muted float-start"></i>
                                        Edit</a></li>
                                <li><a class="dropdown-item" data-bs-toggle="modal" href="javascript:void(0);"><i
                                            class="ri-delete-bin-5-line align-bottom me-2 text-muted float-start"></i>
                                        Delete</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <h6 class="fs-14 text-truncate task-title"><a routerLink="/tasks/details" class="d-block">Banner
                            Design for
                            FB &amp; Twitter</a></h6>
                    <div class="tasks-img rounded" style="background-image: url('assets/images/small/img-4.jpg');">
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 d-flex gap-1">
                            <span class="badge bg-primary-subtle text-primary">UI/UX</span>
                            <span class="badge bg-primary-subtle text-primary">Graphic</span>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-group">
                                <a href="javascript: void(0);" class="avatar-group-item" data-bs-toggle="tooltip"
                                    data-bs-trigger="hover" data-bs-placement="top" aria-label="Frank"
                                    data-bs-original-title="Frank">
                                    <img src="assets/images/users/avatar-3.jpg" alt=""
                                        class="rounded-circle avatar-xxs">
                                </a>
                                <a href="javascript: void(0);" class="avatar-group-item" data-bs-toggle="tooltip"
                                    data-bs-trigger="hover" data-bs-placement="top" aria-label="Herbert"
                                    data-bs-original-title="Herbert">
                                    <img src="assets/images/users/avatar-2.jpg" alt=""
                                        class="rounded-circle avatar-xxs">
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer border-top-dashed">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <span class="text-muted">
                                <i class="ri-time-line align-bottom"></i> 07 Jan, 2022</span>
                        </div>
                        <div class="flex-shrink-0">
                            <ul class="link-inline mb-0 p-0">
                                <li class="list-inline-item">
                                    <a href="javascript:void(0)" class="text-muted"><i
                                            class="ri-eye-line align-bottom"></i> 11</a>
                                </li>
                                <li class="list-inline-item">
                                    <a href="javascript:void(0)" class="text-muted"><i
                                            class="ri-question-answer-line align-bottom"></i> 26</a>
                                </li>
                                <li class="list-inline-item">
                                    <a href="javascript:void(0)" class="text-muted"><i
                                            class="ri-attachment-2 align-bottom"></i> 30</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 55%" aria-valuenow="55"
                        aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>

        </ngx-simplebar>
        <div class="my-3">
            <button class="btn btn-soft-info w-100" data-bs-toggle="modal" data-bs-target="#creatertaskModal"
                (click)="openAddModal(addcontent)">Add More</button>
        </div>
    </div><!--end tasks-list-->
</div><!--end task-board-->

<div id="elmLoader">
    <div class="spinner-border text-primary avatar-sm" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<ng-template #membercontent role="document" let-modal>
    <div class="modal-header p-3 bg-warning-subtle">
        <h5 class="modal-title" id="addmemberModalLabel">Add Member</h5>
        <button type="button" class="btn-close" id="btn-close-member" data-bs-dismiss="modal" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
        <form>
            <div class="row g-3">
                <div class="col-lg-12">
                    <label for="submissionidInput" class="form-label">Submission ID</label>
                    <input type="number" class="form-control" id="submissionidInput" placeholder="Submission ID">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="profileimgInput" class="form-label">Profile Images</label>
                    <input class="form-control" type="file" id="profileimgInput">
                </div><!--end col-->
                <div class="col-lg-6">
                    <label for="firstnameInput" class="form-label">First Name</label>
                    <input type="text" class="form-control" id="firstnameInput" placeholder="Enter firstname">
                </div><!--end col-->
                <div class="col-lg-6">
                    <label for="lastnameInput" class="form-label">Last Name</label>
                    <input type="text" class="form-control" id="lastnameInput" placeholder="Enter lastname">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="designationInput" class="form-label">Designation</label>
                    <input type="text" class="form-control" id="designationInput" placeholder="Designation">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="titleInput" class="form-label">Title</label>
                    <input type="text" class="form-control" id="titleInput" placeholder="Title">
                </div><!--end col-->
                <div class="col-lg-6">
                    <label for="numberInput" class="form-label">Phone Number</label>
                    <input type="text" class="form-control" id="numberInput" placeholder="Phone number">
                </div><!--end col-->
                <div class="col-lg-6">
                    <label for="joiningdateInput" class="form-label">Joining Date</label>
                    <input class="form-control flatpickr-input" type="text" mwlFlatpickr [altInput]="true"
                        [convertModelValue]="true" placeholder="Select date">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="emailInput" class="form-label">Email ID</label>
                    <input type="email" class="form-control" id="emailInput" placeholder="Email">
                </div><!--end col-->
            </div><!--end row-->
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal" (click)="modal.close('Close click')"><i
                class="ri-close-line align-bottom me-1"></i> Close</button>
        <button type="button" class="btn btn-success" id="addMember">Add Member</button>
    </div>
</ng-template>

<ng-template #content role="document" let-modal>
    <div class="modal-header p-3 bg-info-subtle">
        <h5 class="modal-title" id="createboardModalLabel">Add Board</h5>
        <button type="button" class="btn-close" id="btn-close2" data-bs-dismiss="modal" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
        <form action="#">
            <div class="row">
                <div class="col-lg-12">
                    <label for="boardName" class="form-label">Board Name</label>
                    <input type="text" class="form-control" id="boardName" placeholder="Enter board name">
                </div>
                <div class="mt-4">
                    <div class="hstack gap-2 justify-content-end">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                            (click)="modal.close('Close click')">Close</button>
                        <button type="button" class="btn btn-success" id="addNewBoard">Add Board</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</ng-template>

<ng-template #addcontent role="document" let-modal>
    <div class="modal-header p-3 bg-info-subtle">
        <h5 class="modal-title" id="creatertaskModalLabel">Create New Task</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
        <form action="#">
            <div class="row g-3">
                <div class="col-lg-12">
                    <label for="projectName" class="form-label">Project Name</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Enter project name">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="sub-tasks" class="form-label">Task Title</label>
                    <input type="text" class="form-control" id="sub-tasks" placeholder="Task title">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="task-description" class="form-label">Task Description</label>
                    <textarea class="form-control" id="task-description" rows="3"></textarea>
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="formFile" class="form-label">Tasks Images</label>
                    <input class="form-control" type="file" id="formFile">
                </div><!--end col-->
                <div class="col-lg-12">
                    <label for="tasks-progress" class="form-label">Add Team Member</label>
                    <ngx-simplebar style="height: 95px;">
                        <ul class="list-unstyled vstack gap-2 mb-0">
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="anna-adame">
                                    <label class="form-check-label d-flex align-items-center" for="anna-adame">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-1.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Anna Adame
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="frank-hook">
                                    <label class="form-check-label d-flex align-items-center" for="frank-hook">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-3.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Frank Hook
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="alexis-clarke">
                                    <label class="form-check-label d-flex align-items-center" for="alexis-clarke">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-6.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Alexis Clarke
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="herbert-stokes">
                                    <label class="form-check-label d-flex align-items-center" for="herbert-stokes">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-2.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Herbert Stokes
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="michael-morris">
                                    <label class="form-check-label d-flex align-items-center" for="michael-morris">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-7.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Michael Morris
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="nancy-martino">
                                    <label class="form-check-label d-flex align-items-center" for="nancy-martino">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-5.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Nancy Martino
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="thomas-taylor">
                                    <label class="form-check-label d-flex align-items-center" for="thomas-taylor">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-8.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Thomas Taylor
                                        </span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check d-flex align-items-center">
                                    <input class="form-check-input me-3" type="checkbox" value="" id="tonya-noble">
                                    <label class="form-check-label d-flex align-items-center" for="tonya-noble">
                                        <span class="flex-shrink-0">
                                            <img src="assets/images/users/avatar-10.jpg" alt=""
                                                class="avatar-xxs rounded-circle" />
                                        </span>
                                        <span class="flex-grow-1 ms-2">
                                            Tonya Noble
                                        </span>
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </ngx-simplebar>
                </div><!--end col-->
                <div class="col-lg-4">
                    <label for="due-date" class="form-label">Due Date</label>
                    <input class="form-control flatpickr-input" type="text" placeholder="Enter publish date"
                        mwlFlatpickr [altInput]="true" [convertModelValue]="true" [dateFormat]="'Y-m-d'">

                </div><!--end col-->
                <div class="col-lg-4">
                    <label for="categories" class="form-label">Tags</label>
                    <input type="text" class="form-control" id="categories" placeholder="Enter tag">
                </div><!--end col-->
                <div class="col-lg-4">
                    <label for="tasks-progress" class="form-label">Tasks Progress</label>
                    <input type="text" class="form-control" maxlength="3" id="tasks-progress"
                        placeholder="Enter progress">
                </div><!--end col-->
                <div class="mt-4">
                    <div class="hstack gap-2 justify-content-end">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                            (click)="modal.close('Close click')">Close</button>
                        <button type="button" class="btn btn-success">Add Task</button>
                    </div>
                </div><!--end col-->
            </div><!--end row-->
        </form>
    </div>
</ng-template>

<!-- Task  -->
<ng-template #TaskContent let-task='task'>
    <div class="card tasks-box">
        <div class="card-body">
            <div class="d-flex mb-2">
                <h6 class="fs-14 mb-0 flex-grow-1 text-truncate"><a href="tasks/details"
                        class="text-body">{{task.title}}</a></h6>
                <div class="dropdown" ngbDropdown>
                    <a href="javascript:void(0);" class="text-muted arrow-none" id="dropdownMenuLink1"
                        data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle><i
                            class="ri-more-fill"></i></a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                        <li><a class="dropdown-item" href="tasks/details"><i
                                    class="ri-eye-fill align-bottom me-2 text-muted"></i> View</a></li>
                        <li><a class="dropdown-item" href="javascript:void(0);"><i
                                    class="ri-edit-2-line align-bottom me-2 text-muted"></i> Edit</a></li>
                        <li><a class="dropdown-item" data-bs-toggle="modal" (click)="confirm($event)"><i
                                    class="ri-delete-bin-5-line align-bottom me-2 text-muted"></i> Delete</a></li>
                    </ul>
                </div>
            </div>
            <p class="text-muted">{{task.content}}</p>
            @if(task.progress != 0){
            <div class="mb-3">
                <div class="d-flex mb-1">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-0"><span class="text-secondary">{{task.progress}}%</span> of 100%</h6>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="text-muted"><i class="ri-time-line align-bottom"></i>{{task.date}}</span>
                    </div>
                </div>
                <ngb-progressbar [value]="task.progress" type="danger" class="progress-sm"></ngb-progressbar>
            </div>
        }
            <div class="d-flex align-items-center">
                <div class="flex-grow-1 d-flex gap-1">
                    @for(role of task.roles; track $index){
                    <span class="badge bg-primary-subtle text-primary">{{role}}</span>
                    }
                </div>
                <div class="flex-shrink-0">
                    <div class="avatar-group">
                        @for(user of task.users; track $index){
                        <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="{{user.name}}"
                            placement="top">
                            <img src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                        </a>
                    }
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer border-top-dashed">
            <div class="d-flex">
                <div class="flex-grow-1">
                    @if(task.progress == 0){
                        <span class="text-muted mb-0"> <i
                            class="ri-time-line align-bottom me-1"></i>{{task.date}}</span>
                    }@else {
                        <h6 class="text-muted mb-0">{{task.id}}</h6>
                    }
                </div>
                <div class="flex-shrink-0">
                    <ul class="link-inline mb-0">
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-eye-line align-bottom"></i>
                                {{task.view}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i
                                    class="ri-question-answer-line align-bottom"></i> {{task.comment}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-attachment-2 align-bottom"></i>
                                {{task.pin}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div><!--end card-body-->
    </div><!--end card-->
</ng-template>

<!-- InProgress Task  -->
<ng-template #TaskInProgressContent let-task='task'>
    <div class="card tasks-box">
        <div class="card-body">
            <div class="d-flex mb-2">
                <a href="javascript:void(0)" class="text-muted fw-medium fs-14 flex-grow-1">{{task.id}}</a>
                <div class="dropdown" ngbDropdown>
                    <a href="javascript:void(0);" class="text-muted arrow-none" id="dropdownMenuLink1"
                        data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle><i
                            class="ri-more-fill"></i></a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                        <li><a class="dropdown-item" href="tasks/details"><i
                                    class="ri-eye-fill align-bottom me-2 text-muted"></i> View</a></li>
                        <li><a class="dropdown-item" href="javascript:void(0);"><i
                                    class="ri-edit-2-line align-bottom me-2 text-muted"></i> Edit</a></li>
                        <li><a class="dropdown-item" data-bs-toggle="modal" (click)="confirm($event)"><i
                                    class="ri-delete-bin-5-line align-bottom me-2 text-muted"></i> Delete</a></li>
                    </ul>
                </div>
            </div>
            <h6 class="fs-14 text-truncate"><a href="tasks/details">{{task.title}}</a></h6>
            <p class="text-muted">{{task.content}}</p>
            <div class="d-flex align-items-center">
                <div class="flex-grow-1 ">
                    <div class="d-flex gap-1">
                        @for(role of task.roles; track $index){
                        <span class="badge bg-primary-subtle text-primary">{{role}}</span>
                        }
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <div class="avatar-group">
                        @for(user of task.users; track $index){
                        <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="{{user.name}}"
                            placement="top">
                            <img src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                        </a>
                    }
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer border-top-dashed">
            <div class="d-flex">
                <div class="flex-grow-1">
                    <span class="text-muted"><i class="ri-time-line align-bottom"></i>
                        {{task.date}}</span>
                </div>
                <div class="flex-shrink-0">
                    <ul class="link-inline mb-0">
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-eye-line align-bottom"></i>
                                {{task.view}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i
                                    class="ri-question-answer-line align-bottom"></i>
                                {{task.comment}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-attachment-2 align-bottom"></i>
                                {{task.pin}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!--end card-body-->
        <ngb-progressbar [value]="task.progress" type="{{task.variant}}" class="progress-sm"></ngb-progressbar>
    </div>
</ng-template>

<!-- InReviews Task  -->
<ng-template #TaskInReviewsContent let-task='task'>
    <div class="card tasks-box">
        <div class="card-body">
            <div class="d-flex mb-2">
                <a href="javascript:void(0)" class="text-muted fw-medium fs-14 flex-grow-1">{{task.id}}</a>
                <div class="dropdown" ngbDropdown>
                    <a href="javascript:void(0);" class="text-muted arrow-none" id="dropdownMenuLink1"
                        data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle><i
                            class="ri-more-fill"></i></a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                        <li><a class="dropdown-item" href="tasks/details"><i
                                    class="ri-eye-fill align-bottom me-2 text-muted"></i> View</a></li>
                        <li><a class="dropdown-item" href="javascript:void(0);"><i
                                    class="ri-edit-2-line align-bottom me-2 text-muted"></i> Edit</a></li>
                        <li><a class="dropdown-item" data-bs-toggle="modal" (click)="confirm($event)"><i
                                    class="ri-delete-bin-5-line align-bottom me-2 text-muted"></i> Delete</a></li>
                    </ul>
                </div>
            </div>
            <h6 class="fs-14 text-truncate"><a href="tasks/details">{{task.title}}</a></h6>
            @if(task.image){
                <img src="{{task.image}}" class="tasks-img rounded" />
            }@else{
                <p class="text-muted">{{task.content}}</p>
            }
           
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="d-flex gap-1">
                        @for(role of task.roles; track $index){
                        <span class="badge bg-primary-subtle text-primary">{{role}}</span>
                        }
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <div class="avatar-group">
                        @for(user of task.users; track $index){
                        <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="{{user.name}}"
                            placement="top">
                            <img src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                        </a>
                    }
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer border-top-dashed">
            <div class="d-flex">
                <div class="flex-grow-1">
                    <span class="text-muted"><i class="ri-time-line align-bottom"></i>
                        {{task.date}}</span>
                </div>
                <div class="flex-shrink-0">
                    <ul class="link-inline mb-0">
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-eye-line align-bottom"></i>
                                {{task.view}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i
                                    class="ri-question-answer-line align-bottom"></i>
                                {{task.comment}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-attachment-2 align-bottom"></i>
                                {{task.pin}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!--end card-body-->
        <ngb-progressbar [value]="task.progress" type="{{task.variant}}" class="progress-sm"></ngb-progressbar>
    </div>
</ng-template>

<!-- Completed Task  -->
<ng-template #TaskCompletedContent let-task='task'>
    <div class="card tasks-box">
        <div class="card-body">
            <div class="d-flex mb-2">
                <a href="javascript:void(0)" class="text-muted fw-medium fs-14 flex-grow-1">{{task.id}}</a>
                <div class="dropdown" ngbDropdown>
                    <a href="javascript:void(0);" class="text-muted arrow-none" id="dropdownMenuLink1"
                        data-bs-toggle="dropdown" aria-expanded="false" ngbDropdownToggle><i
                            class="ri-more-fill"></i></a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink1" ngbDropdownMenu>
                        <li><a class="dropdown-item" href="tasks/details"><i
                                    class="ri-eye-fill align-bottom me-2 text-muted"></i> View</a></li>
                        <li><a class="dropdown-item" href="javascript:void(0);"><i
                                    class="ri-edit-2-line align-bottom me-2 text-muted"></i> Edit</a></li>
                        <li><a class="dropdown-item" data-bs-toggle="modal" (click)="confirm($event)"><i
                                    class="ri-delete-bin-5-line align-bottom me-2 text-muted"></i> Delete</a></li>
                    </ul>
                </div>
            </div>
            <h6 class="fs-14 text-truncate"><a href="tasks/details">{{task.title}}</a></h6>
            @if(task.image){
            <img src="{{task.image}}" class="tasks-img rounded" />
            }@else{
                <p class="text-muted">{{task.content}}</p>
            }
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="d-flex gap-1">
                        @for(role of task.roles; track $index){
                        <span class="badge bg-primary-subtle text-primary">{{role}}</span>
                        }
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <div class="avatar-group">
                        @for(user of task.users; track $index){
                        <a href="javascript: void(0);" class="avatar-group-item" ngbTooltip="{{user.name}}"
                            placement="top">
                            <img src="{{user.profile}}" alt="" class="rounded-circle avatar-xxs">
                        </a>
                    }
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer border-top-dashed">
            <div class="d-flex">
                <div class="flex-grow-1">
                    <span class="text-muted"><i class="ri-time-line align-bottom"></i>
                        {{task.date}}</span>
                </div>
                <div class="flex-shrink-0">
                    <ul class="link-inline mb-0">
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-eye-line align-bottom"></i>
                                {{task.view}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i
                                    class="ri-question-answer-line align-bottom"></i>
                                {{task.comment}}</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="javascript:void(0)" class="text-muted"><i class="ri-attachment-2 align-bottom"></i>
                                {{task.pin}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!--end card-body-->
        <ngb-progressbar [value]="task.progress" type="{{task.variant}}" class="progress-sm"></ngb-progressbar>
    </div>
</ng-template>