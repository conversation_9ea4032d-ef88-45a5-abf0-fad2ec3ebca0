@use 'sass:map';
@use '../core/style/elevation';
@use '../core/tokens/m3-utils';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-timepicker.
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);

  @return (
    base: (
      timepicker-container-shape: map.get($system, corner-extra-small),
    ),
    color: (
      timepicker-container-background-color: map.get($system, surface-container),
      timepicker-container-elevation-shadow: elevation.get-box-shadow(2),
    ),
    typography: (),
    density: (),
  );
}
