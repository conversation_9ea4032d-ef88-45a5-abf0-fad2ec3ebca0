@use '../core/tokens/m3-utils';
@use 'sass:map';
@use '../core/tokens/m3';

/// Generates custom tokens for the dialog.
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);

  // headline-padding:
  // The vertical padding values are a bit weird, because MDC uses a `::before` pseudo
  // element to size the title which in turn means that we can't set a specific padding
  // and get the exact same space out of it. These values were determined through
  // trial and error so that the first line of text is 24px from the top and the bottom
  // of the text is 16px from the content under it.
  @return (
    base: (
      dialog-actions-alignment: flex-end,
      dialog-actions-padding: 16px 24px,
      dialog-container-elevation-shadow: none,
      dialog-container-max-width: 560px,
      dialog-container-min-width: 280px,
      dialog-container-shape: map.get($system, corner-extra-large),
      dialog-container-small-max-width: calc(100vw - 32px),
      dialog-content-padding: 20px 24px,
      dialog-headline-padding: 6px 24px 13px,
      dialog-with-actions-content-padding: 20px 24px 0,
    ),
    color: (
      dialog-container-color: map.get($system, surface),
      dialog-subhead-color: map.get($system, on-surface),
      dialog-supporting-text-color: map.get($system, on-surface-variant),
    ),
    typography: (
      dialog-subhead-font: map.get($system, headline-small-font),
      dialog-subhead-line-height: map.get($system, headline-small-line-height),
      dialog-subhead-size: map.get($system, headline-small-size),
      dialog-subhead-tracking: map.get($system, headline-small-tracking),
      dialog-subhead-weight: map.get($system, headline-small-weight),
      dialog-supporting-text-font: map.get($system, body-medium-font),
      dialog-supporting-text-line-height: map.get($system, body-medium-line-height),
      dialog-supporting-text-size: map.get($system, body-medium-size),
      dialog-supporting-text-tracking: map.get($system, body-medium-tracking),
      dialog-supporting-text-weight: map.get($system, body-medium-weight),
    ),
    density: (),
  );
}
