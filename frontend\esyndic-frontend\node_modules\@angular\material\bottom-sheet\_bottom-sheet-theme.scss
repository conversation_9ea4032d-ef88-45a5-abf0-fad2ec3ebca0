@use 'sass:map';
@use '../core/typography/typography';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';
@use './m2-bottom-sheet';
@use './m3-bottom-sheet';

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-bottom-sheet.get-tokens($theme), base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-bottom-sheet.get-unthemable-tokens());
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-bottom-sheet.get-tokens($theme), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(
        m2-bottom-sheet.get-color-tokens($theme)
      );
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-bottom-sheet.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(
        m2-bottom-sheet.get-typography-tokens($theme)
      );
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-bottom-sheet.get-tokens($theme), density));
  } @else {
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: bottom-sheet,
      tokens: token-utils.get-overrides(m3-bottom-sheet.get-tokens(), bottom-sheet)
    ),
  );
}

@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-bottom-sheet') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
